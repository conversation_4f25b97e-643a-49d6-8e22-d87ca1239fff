﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GDAPI.Shared\GDAPI.Shared.csproj" />
    <ProjectReference Include="..\GDAPI.Modules\UserManagement\GDAPI.Modules.UserManagement.csproj" />
    <ProjectReference Include="..\GDAPI.Modules\ProductManagement\GDAPI.Modules.ProductManagement.csproj" />
    <ProjectReference Include="..\GDAPI.Modules\AuthoringTool\GDAPI.Modules.AuthoringTool.csproj" />
  </ItemGroup>

</Project>
