using Microsoft.AspNetCore.Mvc;
using CIAPI.Shared.DTOs.Common;
using CIAPI.Shared.Exceptions;
using CIAPI.Modules.AuthoringTool.Application.DTOs;
using CIAPI.Modules.AuthoringTool.Application.Services;

namespace CIAPI.Web.Controllers;

/// <summary>
/// Authoring management controller for AuthoringTool module
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthoringController : ControllerBase
{
    private readonly IAuthoringService _authoringService;
    private readonly ILogger<AuthoringController> _logger;

    public AuthoringController(IAuthoringService authoringService, ILogger<AuthoringController> logger)
    {
        _authoringService = authoringService ?? throw new ArgumentNullException(nameof(authoringService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get paginated authoring items using stored procedure with filtering support
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10, max: 100)</param>
    /// <param name="searchTerm">Search term for filtering authoring items by title or description</param>
    /// <param name="statusFilter">Status filter</param>
    /// <param name="categoryFilter">Category filter</param>
    /// <param name="authorFilter">Author filter</param>
    /// <param name="departmentFilter">Department filter</param>
    /// <returns>Paginated list of authoring items</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<AuthoringDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<PagedResult<AuthoringDto>>>> GetAuthoring(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? statusFilter = null,
        [FromQuery] string? categoryFilter = null,
        [FromQuery] string? authorFilter = null,
        [FromQuery] string? departmentFilter = null)
    {
        _logger.LogInformation("Getting authoring items with pagination. Page: {PageNumber}, Size: {PageSize}, Search: {SearchTerm}",
            pageNumber, pageSize, searchTerm);

        var request = new AuthoringSearchRequest
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            SearchTerm = searchTerm,
            StatusFilter = statusFilter,
            CategoryFilter = categoryFilter,
            AuthorFilter = authorFilter,
            DepartmentFilter = departmentFilter
        };

        var response = await _authoringService.GetAuthoringSamplePagedAsync(request);
        return Ok(response);
    }

    /// <summary>
    /// Health check endpoint for the authoring module
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public ActionResult GetHealth()
    {
        return Ok(new { Status = "Healthy", Module = "Authoring", Timestamp = DateTime.UtcNow });
    }

    #region : Get User Details 

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves user details.

    /// <summary>
    /// Get all users with optional type filter.
    /// </summary>
    /// <param name="type">User type filter (default: "All")</param>
    /// <returns>List of users</returns>
    [HttpGet("users")]
    [ProducesResponseType(typeof(ApiResponse<List<UserDetailsDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<List<UserDetailsDto>>>> GetAllUsers([FromQuery] string type = "All")
    {
        try
        {
            var response = await _authoringService.GetAllUsersAsync(type);

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving users");
            return StatusCode(500, ApiResponse<List<UserDetailsDto>>.ErrorResult("An error occurred while retrieving users"));
        }
    }

    #endregion

    #region : Clinical Comparators

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves Clinical Comparators details.


    /// <summary>
    /// Get clinical comparators grouped by indications.
    /// </summary>
    /// <returns>API response containing a collection of clinical comparators grouped by indications.</returns>
    [HttpGet("clinical-comparators")]
    [ProducesResponseType(typeof(ApiResponse<IEnumerable<ClinicalComparatorsGroupByIndicationsDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<IEnumerable<ClinicalComparatorsGroupByIndicationsDto>>>> GetClinicalComparatorsAsync()
    {
        try
        {
            var response = await _authoringService.GetClinicalComparatorsAsync();

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving clinical comparators");
            return StatusCode(500, ApiResponse<IEnumerable<ClinicalComparatorsGroupByIndicationsDto>>.ErrorResult("An error occurred while retrieving clinical comparators"));
        }
    }


    #endregion
}
