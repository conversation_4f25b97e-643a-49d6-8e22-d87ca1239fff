-- =============================================
-- Global Exception Handling - Error Logging Database Schema
-- =============================================

USE [CISolution]
GO

PRINT 'Starting Error Logging Database Setup...';
GO

-- =============================================
-- 1. CREATE ERROR LOGS TABLE
-- =============================================

-- Create ErrorLogs table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ErrorLogs' AND xtype='U')
BEGIN
    CREATE TABLE ErrorLogs (
        Id int IDENTITY(1,1) PRIMARY KEY,
        ErrorCode nvarchar(100) NOT NULL,
        Message nvarchar(2000) NOT NULL,
        StackTrace nvarchar(MAX) NULL,
        InnerException nvarchar(MAX) NULL,
        CorrelationId nvarchar(100) NULL,
        Module nvarchar(100) NULL,
        UserId nvarchar(100) NULL,
        RequestPath nvarchar(500) NULL,
        HttpMethod nvarchar(10) NULL,
        UserAgent nvarchar(1000) NULL,
        IpAddress nvarchar(50) NULL,
        Details nvarchar(MAX) NULL,
        Context nvarchar(MAX) NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        Severity nvarchar(20) NOT NULL DEFAULT 'Error',
        Environment nvarchar(50) NULL,
        MachineName nvarchar(100) NULL,
        ProcessId nvarchar(20) NULL,
        ThreadId nvarchar(20) NULL
    );
    
    PRINT 'ErrorLogs table created successfully!';
END
ELSE
BEGIN
    PRINT 'ErrorLogs table already exists.';
END
GO

-- =============================================
-- 2. CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ErrorLogs_CreatedAt')
BEGIN
    CREATE INDEX IX_ErrorLogs_CreatedAt ON ErrorLogs(CreatedAt DESC);
    PRINT 'Index IX_ErrorLogs_CreatedAt created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ErrorLogs_ErrorCode')
BEGIN
    CREATE INDEX IX_ErrorLogs_ErrorCode ON ErrorLogs(ErrorCode);
    PRINT 'Index IX_ErrorLogs_ErrorCode created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ErrorLogs_Module')
BEGIN
    CREATE INDEX IX_ErrorLogs_Module ON ErrorLogs(Module);
    PRINT 'Index IX_ErrorLogs_Module created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ErrorLogs_CorrelationId')
BEGIN
    CREATE INDEX IX_ErrorLogs_CorrelationId ON ErrorLogs(CorrelationId);
    PRINT 'Index IX_ErrorLogs_CorrelationId created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ErrorLogs_Severity')
BEGIN
    CREATE INDEX IX_ErrorLogs_Severity ON ErrorLogs(Severity);
    PRINT 'Index IX_ErrorLogs_Severity created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ErrorLogs_UserId')
BEGIN
    CREATE INDEX IX_ErrorLogs_UserId ON ErrorLogs(UserId);
    PRINT 'Index IX_ErrorLogs_UserId created.';
END
GO

-- =============================================
-- 3. CREATE STORED PROCEDURES
-- =============================================

-- Create the InsertErrorLog stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'InsertErrorLog')
BEGIN
    DROP PROCEDURE InsertErrorLog;
    PRINT 'Existing InsertErrorLog procedure dropped.';
END
GO

CREATE PROCEDURE InsertErrorLog
    @ErrorCode NVARCHAR(100),
    @Message NVARCHAR(2000),
    @StackTrace NVARCHAR(MAX) = NULL,
    @InnerException NVARCHAR(MAX) = NULL,
    @CorrelationId NVARCHAR(100) = NULL,
    @Module NVARCHAR(100) = NULL,
    @UserId NVARCHAR(100) = NULL,
    @RequestPath NVARCHAR(500) = NULL,
    @HttpMethod NVARCHAR(10) = NULL,
    @UserAgent NVARCHAR(1000) = NULL,
    @IpAddress NVARCHAR(50) = NULL,
    @Details NVARCHAR(MAX) = NULL,
    @Context NVARCHAR(MAX) = NULL,
    @Severity NVARCHAR(20) = 'Error',
    @Environment NVARCHAR(50) = NULL,
    @MachineName NVARCHAR(100) = NULL,
    @ProcessId NVARCHAR(20) = NULL,
    @ThreadId NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO ErrorLogs (
            ErrorCode, Message, StackTrace, InnerException, CorrelationId, Module, UserId,
            RequestPath, HttpMethod, UserAgent, IpAddress, Details, Context, Severity,
            Environment, MachineName, ProcessId, ThreadId, CreatedAt
        )
        VALUES (
            @ErrorCode, @Message, @StackTrace, @InnerException, @CorrelationId, @Module, @UserId,
            @RequestPath, @HttpMethod, @UserAgent, @IpAddress, @Details, @Context, @Severity,
            @Environment, @MachineName, @ProcessId, @ThreadId, GETUTCDATE()
        );
        
        SELECT SCOPE_IDENTITY() as ErrorLogId;
        
    END TRY
    BEGIN CATCH
        -- If error logging fails, we don't want to throw another exception
        -- Just log to SQL Server error log
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        -- Log to SQL Server error log
        RAISERROR('Failed to insert error log: %s', @ErrorSeverity, @ErrorState, @ErrorMessage) WITH LOG;
        
        -- Return -1 to indicate failure
        SELECT -1 as ErrorLogId;
    END CATCH
END
GO

PRINT 'InsertErrorLog stored procedure created successfully!';
GO

-- Create the GetErrorLogs stored procedure for retrieving error logs
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'GetErrorLogs')
BEGIN
    DROP PROCEDURE GetErrorLogs;
    PRINT 'Existing GetErrorLogs procedure dropped.';
END
GO

CREATE PROCEDURE GetErrorLogs
    @PageNumber INT = 1,
    @PageSize INT = 50,
    @Module NVARCHAR(100) = NULL,
    @Severity NVARCHAR(20) = NULL,
    @FromDate DATETIME2 = NULL,
    @ToDate DATETIME2 = NULL,
    @CorrelationId NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
        
        -- Get total count
        DECLARE @TotalCount INT;
        SELECT @TotalCount = COUNT(*)
        FROM ErrorLogs
        WHERE (@Module IS NULL OR Module = @Module)
          AND (@Severity IS NULL OR Severity = @Severity)
          AND (@FromDate IS NULL OR CreatedAt >= @FromDate)
          AND (@ToDate IS NULL OR CreatedAt <= @ToDate)
          AND (@CorrelationId IS NULL OR CorrelationId = @CorrelationId);
        
        -- Get paginated results
        SELECT 
            Id, ErrorCode, Message, StackTrace, InnerException, CorrelationId, Module, UserId,
            RequestPath, HttpMethod, UserAgent, IpAddress, Details, Context, CreatedAt,
            Severity, Environment, MachineName, ProcessId, ThreadId,
            @TotalCount as TotalCount
        FROM ErrorLogs
        WHERE (@Module IS NULL OR Module = @Module)
          AND (@Severity IS NULL OR Severity = @Severity)
          AND (@FromDate IS NULL OR CreatedAt >= @FromDate)
          AND (@ToDate IS NULL OR CreatedAt <= @ToDate)
          AND (@CorrelationId IS NULL OR CorrelationId = @CorrelationId)
        ORDER BY CreatedAt DESC
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'GetErrorLogs stored procedure created successfully!';
GO

-- =============================================
-- 4. CREATE CLEANUP PROCEDURE
-- =============================================

-- Create procedure to clean up old error logs
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'CleanupErrorLogs')
BEGIN
    DROP PROCEDURE CleanupErrorLogs;
    PRINT 'Existing CleanupErrorLogs procedure dropped.';
END
GO

CREATE PROCEDURE CleanupErrorLogs
    @RetentionDays INT = 90
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @CutoffDate DATETIME2 = DATEADD(DAY, -@RetentionDays, GETUTCDATE());
        DECLARE @DeletedCount INT;
        
        DELETE FROM ErrorLogs 
        WHERE CreatedAt < @CutoffDate;
        
        SET @DeletedCount = @@ROWCOUNT;
        
        PRINT CONCAT('Cleaned up ', @DeletedCount, ' error log entries older than ', @RetentionDays, ' days.');
        
        SELECT @DeletedCount as DeletedCount, @CutoffDate as CutoffDate;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'CleanupErrorLogs stored procedure created successfully!';
GO

PRINT '==============================================';
PRINT 'Error Logging Database Setup Complete!';
PRINT '==============================================';
PRINT 'Summary:';
PRINT '- ErrorLogs table created with indexes';
PRINT '- InsertErrorLog stored procedure created';
PRINT '- GetErrorLogs stored procedure created';
PRINT '- CleanupErrorLogs stored procedure created';
PRINT '- All components ready for use';
PRINT '==============================================';
GO
