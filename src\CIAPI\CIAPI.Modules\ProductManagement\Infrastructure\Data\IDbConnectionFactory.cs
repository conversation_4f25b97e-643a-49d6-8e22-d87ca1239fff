using System.Data;

namespace GDAPI.Modules.ProductManagement.Infrastructure.Data;

/// <summary>
/// Factory interface for creating database connections for ProductManagement module
/// </summary>
public interface IProductManagementDbConnectionFactory
{
    /// <summary>
    /// Creates a new database connection
    /// </summary>
    /// <returns>Database connection instance</returns>
    IDbConnection CreateConnection();
    
    /// <summary>
    /// Creates a new database connection asynchronously
    /// </summary>
    /// <returns>Database connection instance</returns>
    Task<IDbConnection> CreateConnectionAsync();
    
    /// <summary>
    /// Gets the connection string
    /// </summary>
    string ConnectionString { get; }
}
