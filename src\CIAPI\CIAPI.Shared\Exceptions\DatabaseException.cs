using System.Net;

namespace CIAPI.Shared.Exceptions;

/// <summary>
/// Exception for database-related errors
/// </summary>
public class DatabaseException : BaseException
{
    public override HttpStatusCode StatusCode => HttpStatusCode.InternalServerError;
    public override string ErrorCode => "DATABASE_ERROR";

    /// <summary>
    /// SQL error number if available
    /// </summary>
    public int? SqlErrorNumber { get; set; }
    
    /// <summary>
    /// SQL state if available
    /// </summary>
    public string? SqlState { get; set; }

    public DatabaseException(string message) : base(message)
    {
    }

    public DatabaseException(string message, Exception innerException) : base(message, innerException)
    {
        ExtractSqlErrorInfo(innerException);
    }

    public DatabaseException(string message, string correlationId) : base(message, correlationId)
    {
    }

    public DatabaseException(string message, Exception innerException, string correlationId) 
        : base(message, innerException, correlationId)
    {
        ExtractSqlErrorInfo(innerException);
    }

    private void ExtractSqlErrorInfo(Exception exception)
    {
        // Extract SQL Server error information if available
        if (exception is Microsoft.Data.SqlClient.SqlException sqlEx)
        {
            SqlErrorNumber = sqlEx.Number;
            SqlState = sqlEx.State.ToString();
            Details = new 
            { 
                SqlErrorNumber = sqlEx.Number,
                SqlState = sqlEx.State,
                Severity = sqlEx.Class,
                Procedure = sqlEx.Procedure,
                LineNumber = sqlEx.LineNumber
            };
        }
    }
}

/// <summary>
/// Exception for connection-related database errors
/// </summary>
public class DatabaseConnectionException : DatabaseException
{
    public override string ErrorCode => "DATABASE_CONNECTION_ERROR";

    public DatabaseConnectionException(string message) : base(message)
    {
    }

    public DatabaseConnectionException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public DatabaseConnectionException(string message, string correlationId) : base(message, correlationId)
    {
    }
}

/// <summary>
/// Exception for timeout-related database errors
/// </summary>
public class DatabaseTimeoutException : DatabaseException
{
    public override string ErrorCode => "DATABASE_TIMEOUT_ERROR";

    public DatabaseTimeoutException(string message) : base(message)
    {
    }

    public DatabaseTimeoutException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public DatabaseTimeoutException(string message, string correlationId) : base(message, correlationId)
    {
    }
}
