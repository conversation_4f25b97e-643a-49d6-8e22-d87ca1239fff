[{"ContainingType": "CIAPI.Web.Controllers.AuthController", "Method": "GetCurrentApiKey", "RelativePath": "api/Auth/current", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Shared.Authentication.Models.ApiKeyInfo, CIAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "CIAPI.Web.Controllers.AuthController", "Method": "GenerateApiKey", "RelativePath": "api/Auth/generate-key", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "CIAPI.Web.Controllers.GenerateApiKeyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Web.Controllers.GenerateApiKeyResponse, CIAPI.Web, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "CIAPI.Web.Controllers.AuthController", "Method": "GetApiKeys", "RelativePath": "api/Auth/keys", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[CIAPI.Shared.Authentication.Models.ApiKeyInfo, CIAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "CIAPI.Web.Controllers.AuthController", "Method": "RevokeApiKey", "RelativePath": "api/Auth/revoke-key", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "CIAPI.Web.Controllers.RevokeApiKeyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "CIAPI.Web.Controllers.AuthController", "Method": "ValidateApiKey", "RelativePath": "api/Auth/validate-key", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "CIAPI.Web.Controllers.ValidateApiKeyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Web.Controllers.ApiKeyValidationResponse, CIAPI.Web, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "CIAPI.Web.Controllers.AuthoringController", "Method": "GetAuthoring", "RelativePath": "api/Authoring", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "statusFilter", "Type": "System.String", "IsRequired": false}, {"Name": "categoryFilter", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>er", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Shared.DTOs.Common.PagedResult`1[[CIAPI.Modules.AuthoringTool.Application.DTOs.AuthoringDto, CIAPI.Modules.AuthoringTool, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], CIAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.AuthoringController", "Method": "GetClinicalComparatorsAsync", "RelativePath": "api/Authoring/clinical-comparators", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.IEnumerable`1[[CIAPI.Modules.AuthoringTool.Application.DTOs.ClinicalComparatorsGroupByIndicationsDto, CIAPI.Modules.AuthoringTool, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.AuthoringController", "Method": "GetHealth", "RelativePath": "api/Authoring/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "CIAPI.Web.Controllers.AuthoringController", "Method": "GetAllUsers", "RelativePath": "api/Authoring/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[CIAPI.Modules.AuthoringTool.Application.DTOs.UserDetailsDto, CIAPI.Modules.AuthoringTool, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetInvestigators", "RelativePath": "api/Investigators", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "regionFilter", "Type": "System.String", "IsRequired": false}, {"Name": "countryF<PERSON>er", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "CIAPI.Shared.DTOs.Common.ApiResponse`1[[CIAPI.Shared.DTOs.Common.PagedResult`1[[CIAPI.Modules.ProductManagement.Application.DTOs.InvestigatorDto, CIAPI.Modules.ProductManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], CIAPI.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "CIAPI.Shared.DTOs.Common.ApiResponse", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "CIAPI.Web.Controllers.InvestigatorsController", "Method": "GetHealth", "RelativePath": "api/Investigators/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_3", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "<>f__AnonymousType1`2[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Health"], "EndpointName": "HealthCheck"}]