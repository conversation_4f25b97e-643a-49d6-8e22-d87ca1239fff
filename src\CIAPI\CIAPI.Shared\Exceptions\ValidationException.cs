using System.Net;

namespace GDAPI.Shared.Exceptions;

/// <summary>
/// Exception for validation errors
/// </summary>
public class ValidationException : BaseException
{
    public override HttpStatusCode StatusCode => HttpStatusCode.BadRequest;
    public override string ErrorCode => "VALIDATION_ERROR";

    /// <summary>
    /// Validation errors details
    /// </summary>
    public Dictionary<string, string[]> ValidationErrors { get; set; } = new();

    public ValidationException(string message) : base(message)
    {
    }

    public ValidationException(string message, Dictionary<string, string[]> validationErrors) : base(message)
    {
        ValidationErrors = validationErrors;
        Details = validationErrors;
    }

    public ValidationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public ValidationException(string message, string correlationId) : base(message, correlationId)
    {
    }

    public ValidationException(string message, Dictionary<string, string[]> validationErrors, string correlationId) 
        : base(message, correlationId)
    {
        ValidationErrors = validationErrors;
        Details = validationErrors;
    }

    /// <summary>
    /// Add a validation error for a specific field
    /// </summary>
    public ValidationException AddError(string field, string error)
    {
        if (!ValidationErrors.ContainsKey(field))
            ValidationErrors[field] = new[] { error };
        else
            ValidationErrors[field] = ValidationErrors[field].Append(error).ToArray();
        
        Details = ValidationErrors;
        return this;
    }

    /// <summary>
    /// Add multiple validation errors for a specific field
    /// </summary>
    public ValidationException AddErrors(string field, string[] errors)
    {
        ValidationErrors[field] = errors;
        Details = ValidationErrors;
        return this;
    }
}
