# Exception Handling Testing Script
# This script tests various exception scenarios in the CI API

param(
    [string]$BaseUrl = "http://localhost:5199",
    [switch]$Verbose
)

Write-Host "🧪 Testing Global Exception Handling System" -ForegroundColor Cyan
Write-Host "Base URL: $BaseUrl" -ForegroundColor Gray
Write-Host ""

# Function to make HTTP request and display results
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Description,
        [int]$ExpectedStatusCode = 200
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $response = Invoke-WebRequest -Uri $Url -Method GET -UseBasicParsing -ErrorAction SilentlyContinue
        $statusCode = $response.StatusCode
        $content = $response.Content | ConvertFrom-Json
        
        Write-Host "Status: $statusCode" -ForegroundColor $(if ($statusCode -eq $ExpectedStatusCode) { "Green" } else { "Red" })
        
        if ($Verbose) {
            Write-Host "Response:" -ForegroundColor Gray
            $content | ConvertTo-Json -Depth 10 | Write-Host -ForegroundColor Gray
        } else {
            Write-Host "Success: $($content.success)" -ForegroundColor $(if ($content.success) { "Green" } else { "Red" })
            Write-Host "Message: $($content.message)" -ForegroundColor Gray
            if ($content.error) {
                Write-Host "Error Code: $($content.error.code)" -ForegroundColor Red
                Write-Host "Correlation ID: $($content.error.correlationId)" -ForegroundColor Gray
            }
        }
    }
    catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $responseBody = $reader.ReadToEnd()
        
        Write-Host "Status: $statusCode" -ForegroundColor $(if ($statusCode -eq $ExpectedStatusCode) { "Green" } else { "Red" })
        
        if ($responseBody) {
            try {
                $content = $responseBody | ConvertFrom-Json
                if ($Verbose) {
                    Write-Host "Response:" -ForegroundColor Gray
                    $content | ConvertTo-Json -Depth 10 | Write-Host -ForegroundColor Gray
                } else {
                    Write-Host "Success: $($content.success)" -ForegroundColor $(if ($content.success) { "Green" } else { "Red" })
                    Write-Host "Message: $($content.message)" -ForegroundColor Gray
                    if ($content.error) {
                        Write-Host "Error Code: $($content.error.code)" -ForegroundColor Red
                        Write-Host "Correlation ID: $($content.error.correlationId)" -ForegroundColor Gray
                    }
                }
            }
            catch {
                Write-Host "Raw Response: $responseBody" -ForegroundColor Red
            }
        }
    }
    
    Write-Host ""
    Start-Sleep -Seconds 1
}

# Test 1: Valid Request (Should succeed)
Write-Host "=== Test 1: Valid Request ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring" -Description "Get authoring items (valid request)" -ExpectedStatusCode 200

# Test 2: Validation Error - Invalid Page Number
Write-Host "=== Test 2: Validation Error ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring?pageNumber=0" -Description "Invalid page number (should be > 0)" -ExpectedStatusCode 400

# Test 3: Validation Error - Invalid Page Size
Write-Host "=== Test 3: Validation Error ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring?pageSize=101" -Description "Invalid page size (should be <= 100)" -ExpectedStatusCode 400

# Test 4: Validation Error - Negative Page Size
Write-Host "=== Test 4: Validation Error ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring?pageSize=-1" -Description "Negative page size" -ExpectedStatusCode 400

# Test 5: Health Check (Should succeed)
Write-Host "=== Test 5: Health Check ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring/health" -Description "Health check endpoint" -ExpectedStatusCode 200

# Test 6: Non-existent Endpoint (Should return 404)
Write-Host "=== Test 6: Not Found ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/nonexistent" -Description "Non-existent endpoint" -ExpectedStatusCode 404

# Test 7: Test with Search Parameters
Write-Host "=== Test 7: Search Functionality ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring?searchTerm=test&pageSize=5" -Description "Search with valid parameters" -ExpectedStatusCode 200

# Test 8: Test with Filters
Write-Host "=== Test 8: Filter Functionality ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring?statusFilter=Draft&pageSize=10" -Description "Filter by status" -ExpectedStatusCode 200

# Test 9: Combined Invalid Parameters
Write-Host "=== Test 9: Multiple Validation Errors ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring?pageNumber=0&pageSize=101" -Description "Multiple validation errors" -ExpectedStatusCode 400

# Test 10: Edge Case - Very Large Page Number
Write-Host "=== Test 10: Edge Case ===" -ForegroundColor Green
Test-Endpoint -Url "$BaseUrl/api/authoring?pageNumber=999999&pageSize=1" -Description "Very large page number" -ExpectedStatusCode 200

Write-Host "🎉 Exception Handling Tests Completed!" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Yellow
Write-Host "- All requests should return consistent JSON responses" -ForegroundColor Gray
Write-Host "- Error responses should include correlation IDs" -ForegroundColor Gray
Write-Host "- Validation errors should return 400 status codes" -ForegroundColor Gray
Write-Host "- Success responses should return 200 status codes" -ForegroundColor Gray
Write-Host ""
Write-Host "📁 Check the following for logged errors:" -ForegroundColor Yellow
Write-Host "- File logs: Logs/errors-$(Get-Date -Format 'yyyy-MM-dd').log" -ForegroundColor Gray
Write-Host "- Database logs: SELECT * FROM ErrorLogs ORDER BY CreatedAt DESC" -ForegroundColor Gray
Write-Host ""
Write-Host "🔍 To run with verbose output:" -ForegroundColor Yellow
Write-Host ".\test-exception-handling.ps1 -Verbose" -ForegroundColor Gray
