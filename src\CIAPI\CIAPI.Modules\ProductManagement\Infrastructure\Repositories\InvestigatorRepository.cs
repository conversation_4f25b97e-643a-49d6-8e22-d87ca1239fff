using CIAPI.Modules.ProductManagement.Domain.Entities;
using CIAPI.Modules.ProductManagement.Domain.Interfaces;
using CIAPI.Modules.ProductManagement.Infrastructure.Data;
using Dapper;

namespace CIAPI.Modules.ProductManagement.Infrastructure.Repositories;

/// <summary>
/// Investigator repository implementation using ADO.NET with Dapper for ProductManagement module
/// </summary>
public class InvestigatorRepository : IInvestigatorRepository
{
    private readonly IProductManagementDbConnectionFactory _connectionFactory;

    public InvestigatorRepository(IProductManagementDbConnectionFactory connectionFactory)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
    }

    public async Task<(IEnumerable<Investigator> Items, int TotalCount)> GetInvestigatorsSamplePagedAsync(
        int pageNumber = 1,
        int pageSize = 10,
        string? searchTerm = null,
        string? regionFilter = null,
        string? countryFilter = null,
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();

        // First get all data from stored procedure
        var allData = await connection.QueryAsync<Investigator>("EXEC GetInvestigators_Sample");

        // Apply filters in memory (for simplicity)
        var filteredData = allData.AsEnumerable();

        if (!string.IsNullOrEmpty(searchTerm))
        {
            filteredData = filteredData.Where(x => x.InvestigatorName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(regionFilter))
        {
            filteredData = filteredData.Where(x => x.RegionName == regionFilter);
        }

        if (!string.IsNullOrEmpty(countryFilter))
        {
            filteredData = filteredData.Where(x => x.CountryName == countryFilter);
        }

        var totalCount = filteredData.Count();

        // Apply pagination
        var offset = (pageNumber - 1) * pageSize;
        var pagedData = filteredData
            .OrderBy(x => x.InvestigatorName)
            .Skip(offset)
            .Take(pageSize);

        return (pagedData, totalCount);
    }
}
