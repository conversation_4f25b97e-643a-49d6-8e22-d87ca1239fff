-- =============================================
-- AuthoringTool Module - Documents Database Schema
-- =============================================

USE [CISolution]
GO

-- Create Documents table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Documents' AND xtype='U')
BEGIN
    CREATE TABLE Documents (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Title nvarchar(200) NOT NULL,
        Description nvarchar(1000) NULL,
        DocumentType nvarchar(50) NOT NULL,
        Category nvarchar(100) NULL,
        Tags nvarchar(500) NULL,
        Author nvarchar(100) NOT NULL,
        CoAuthors nvarchar(100) NULL,
        Status nvarchar(50) NOT NULL DEFAULT 'Draft',
        Version nvarchar(20) NULL,
        FilePath nvarchar(500) NULL,
        FileName nvarchar(100) NULL,
        FileSize bigint NULL,
        FileFormat nvarchar(50) NULL,
        LastModified datetime2 NULL,
        PublishedDate datetime2 NULL,
        Department nvarchar(100) NULL,
        Project nvarchar(100) NULL,
        Priority nvarchar(50) NULL,
        DueDate datetime2 NULL,
        ReviewComments nvarchar(1000) NULL,
        ReviewedBy nvarchar(100) NULL,
        ReviewedDate datetime2 NULL,
        ApprovedBy nvarchar(100) NULL,
        ApprovedDate datetime2 NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDeleted bit NOT NULL DEFAULT 0,
        RowVersion rowversion
    );
    
    PRINT 'Documents table created successfully!';
END
ELSE
BEGIN
    PRINT 'Documents table already exists.';
END
GO

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Documents_Title')
BEGIN
    CREATE INDEX IX_Documents_Title ON Documents(Title);
    PRINT 'Index IX_Documents_Title created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Documents_Author')
BEGIN
    CREATE INDEX IX_Documents_Author ON Documents(Author);
    PRINT 'Index IX_Documents_Author created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Documents_Status')
BEGIN
    CREATE INDEX IX_Documents_Status ON Documents(Status);
    PRINT 'Index IX_Documents_Status created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Documents_Department')
BEGIN
    CREATE INDEX IX_Documents_Department ON Documents(Department);
    PRINT 'Index IX_Documents_Department created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Documents_Category')
BEGIN
    CREATE INDEX IX_Documents_Category ON Documents(Category);
    PRINT 'Index IX_Documents_Category created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Documents_CreatedAt')
BEGIN
    CREATE INDEX IX_Documents_CreatedAt ON Documents(CreatedAt);
    PRINT 'Index IX_Documents_CreatedAt created.';
END
GO

-- Create the GetDocuments_Sample stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'GetDocuments_Sample')
BEGIN
    DROP PROCEDURE GetDocuments_Sample;
    PRINT 'Existing GetDocuments_Sample procedure dropped.';
END
GO

CREATE PROCEDURE GetDocuments_Sample
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Return all documents with the specified columns
        SELECT 
            Id,
            Title,
            Description,
            DocumentType,
            Category,
            Tags,
            Author,
            CoAuthors,
            Status,
            Version,
            FilePath,
            FileName,
            FileSize,
            FileFormat,
            LastModified,
            PublishedDate,
            Department,
            Project,
            Priority,
            DueDate,
            ReviewComments,
            ReviewedBy,
            ReviewedDate,
            ApprovedBy,
            ApprovedDate,
            CreatedAt,
            UpdatedAt,
            CreatedBy,
            UpdatedBy,
            IsActive,
            IsDeleted,
            RowVersion
        FROM Documents
        WHERE IsDeleted = 0
        ORDER BY CreatedAt DESC;
        
    END TRY
    BEGIN CATCH
        -- Error handling
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'GetDocuments_Sample stored procedure created successfully!';
GO
