using System.Net;

namespace CIAPI.Shared.Exceptions;

/// <summary>
/// Exception for unauthorized access attempts
/// </summary>
public class UnauthorizedException : BaseException
{
    public override HttpStatusCode StatusCode => HttpStatusCode.Unauthorized;
    public override string ErrorCode => "UNAUTHORIZED_ACCESS";

    public UnauthorizedException(string message) : base(message)
    {
    }

    public UnauthorizedException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public UnauthorizedException(string message, string correlationId) : base(message, correlationId)
    {
    }

    public UnauthorizedException() : base("Access denied. Authentication required.")
    {
    }
}

/// <summary>
/// Exception for forbidden access (authenticated but not authorized)
/// </summary>
public class ForbiddenException : BaseException
{
    public override HttpStatusCode StatusCode => HttpStatusCode.Forbidden;
    public override string ErrorCode => "FORBIDDEN_ACCESS";

    public ForbiddenException(string message) : base(message)
    {
    }

    public ForbiddenException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public ForbiddenException(string message, string correlationId) : base(message, correlationId)
    {
    }

    public ForbiddenException() : base("Access denied. Insufficient permissions.")
    {
    }
}
