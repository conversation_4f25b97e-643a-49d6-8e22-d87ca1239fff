using Microsoft.AspNetCore.Mvc;
using GDAPI.Shared.DTOs.Common;
using GDAPI.Modules.ProductManagement.Application.DTOs;
using GDAPI.Modules.ProductManagement.Application.Services;

namespace GDAPI.Web.Controllers;

/// <summary>
/// Investigators management controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class InvestigatorsController : ControllerBase
{
    private readonly IInvestigatorService _investigatorService;
    private readonly ILogger<InvestigatorsController> _logger;

    public InvestigatorsController(IInvestigatorService investigatorService, ILogger<InvestigatorsController> logger)
    {
        _investigatorService = investigatorService ?? throw new ArgumentNullException(nameof(investigatorService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Get paginated investigators using stored procedure with filtering support
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10, max: 100)</param>
    /// <param name="searchTerm">Search term for filtering investigators by name</param>
    /// <param name="regionFilter">Region filter</param>
    /// <param name="countryFilter">Country filter</param>
    /// <returns>Paginated list of investigators</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<InvestigatorDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<PagedResult<InvestigatorDto>>>> GetInvestigators(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? regionFilter = null,
        [FromQuery] string? countryFilter = null)
    {
        try
        {
            _logger.LogInformation("Getting investigators with pagination. Page: {PageNumber}, Size: {PageSize}, Search: {SearchTerm}",
                pageNumber, pageSize, searchTerm);

            // Validate pagination parameters
            if (pageNumber < 1)
                return BadRequest(ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("Page number must be greater than 0"));

            if (pageSize < 1 || pageSize > 100)
                return BadRequest(ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("Page size must be between 1 and 100"));

            var request = new InvestigatorSearchRequest
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm,
                RegionFilter = regionFilter,
                CountryFilter = countryFilter
            };

            var response = await _investigatorService.GetInvestigatorsSamplePagedAsync(request);

            if (response.Success)
                return Ok(response);

            return StatusCode(500, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting investigators");
            return StatusCode(500, ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators"));
        }
    }

    /// <summary>
    /// Health check endpoint for the investigators module
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public ActionResult GetHealth()
    {
        return Ok(new { Status = "Healthy", Module = "Investigators", Timestamp = DateTime.UtcNow });
    }
}
