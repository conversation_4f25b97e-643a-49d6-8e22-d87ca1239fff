# API Key Authentication System

## 🎯 **Overview**

The CI Solution now implements a comprehensive **API Key Authentication** system that provides secure access control for all API endpoints. This system is designed for beta deployment and can be easily extended for production use.

## 🔑 **Key Features**

### ✅ **Simple & Secure**
- **Single Master Key**: `ci-solution-beta-key-2024` (configurable)
- **Header-based Authentication**: Uses `X-API-Key` header
- **Rate Limiting**: 1000 requests per hour per key (configurable)
- **Request Tracking**: Logs all API usage with timestamps

### ✅ **Production Ready**
- **Hashed Storage**: API keys are hashed using SHA-256
- **Rate Limiting**: Prevents API abuse
- **Usage Logging**: Comprehensive request tracking
- **Error Handling**: Consistent error responses
- **CORS Support**: Works with web applications

### ✅ **Developer Friendly**
- **Swagger Integration**: API key input in Swagger UI
- **Management Endpoints**: Generate, validate, and revoke keys
- **Clear Error Messages**: Helpful debugging information
- **Easy Configuration**: Simple appsettings.json setup

## 🚀 **Quick Start**

### **1. Your Master API Key**
```
X-API-Key: ci-solution-beta-key-2024
```

### **2. Test the API**
```bash
# Test with curl
curl -H "X-API-Key: ci-solution-beta-key-2024" \
     http://localhost:5199/api/authoring

# Test health endpoint
curl -H "X-API-Key: ci-solution-beta-key-2024" \
     http://localhost:5199/api/authoring/health
```

### **3. Use in Swagger UI**
1. Open `http://localhost:5199`
2. Click the **"Authorize"** button
3. Enter your API key: `ci-solution-beta-key-2024`
4. Click **"Authorize"**
5. All requests will now include the API key

## 📋 **API Endpoints**

### **Protected Endpoints** (Require API Key)
- `GET /api/authoring` - Get authoring items
- `GET /api/authoring/health` - Health check
- `GET /api/products` - Get products (if implemented)
- All other API endpoints

### **Public Endpoints** (No API Key Required)
- `GET /health` - Application health
- `GET /swagger` - API documentation
- `POST /api/auth/generate-key` - Generate new API keys
- `POST /api/auth/validate-key` - Validate API keys

## 🔧 **Configuration**

### **appsettings.json**
```json
{
  "Authentication": {
    "ApiKey": {
      "HeaderName": "X-API-Key",
      "RequireHttps": false,
      "DefaultRateLimit": 1000,
      "RateLimitWindowMinutes": 60,
      "EnableRateLimit": true,
      "MasterApiKey": "ci-solution-beta-key-2024",
      "LogUsage": true
    }
  }
}
```

### **Configuration Options**
- **`HeaderName`**: HTTP header name for API key (default: `X-API-Key`)
- **`RequireHttps`**: Require HTTPS for API key auth (default: `false` for beta)
- **`DefaultRateLimit`**: Requests per window (default: `1000`)
- **`RateLimitWindowMinutes`**: Rate limit window in minutes (default: `60`)
- **`EnableRateLimit`**: Enable/disable rate limiting (default: `true`)
- **`MasterApiKey`**: Your master API key for beta testing
- **`LogUsage`**: Log API key usage (default: `true`)

## 🛡️ **Security Features**

### **Rate Limiting**
- **Default Limit**: 1000 requests per hour
- **Per-Key Tracking**: Each API key has its own rate limit
- **IP-based Tracking**: Rate limits are also tracked per IP address
- **Headers**: Rate limit info in response headers
  ```
  X-RateLimit-Limit: 1000
  X-RateLimit-Remaining: 999
  X-RateLimit-Reset: 1642694400
  ```

### **Request Tracking**
- **Usage Logging**: All API requests are logged
- **IP Address Tracking**: Client IP addresses are recorded
- **Timestamp Tracking**: Last used timestamps are updated
- **User Agent Logging**: Client information is captured

### **Error Responses**
```json
{
  "success": false,
  "message": "Authentication required",
  "error": {
    "code": "UNAUTHORIZED",
    "message": "API key is required",
    "timestamp": "2024-01-20T15:45:30Z"
  },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

## 🔑 **API Key Management**

### **Generate New API Key**
```bash
POST /api/auth/generate-key
Content-Type: application/json

{
  "name": "My Application",
  "clientName": "My Company",
  "rateLimit": 500,
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "apiKey": "generated-api-key-here",
    "name": "My Application",
    "clientName": "My Company",
    "rateLimit": 500,
    "expiresAt": "2024-12-31T23:59:59Z",
    "createdAt": "2024-01-20T15:45:30Z"
  }
}
```

### **Validate API Key**
```bash
POST /api/auth/validate-key
Content-Type: application/json

{
  "apiKey": "your-api-key-here"
}
```

### **Get Current API Key Info**
```bash
GET /api/auth/current
X-API-Key: your-api-key-here
```

### **List All API Keys**
```bash
GET /api/auth/keys
X-API-Key: your-api-key-here
```

### **Revoke API Key**
```bash
POST /api/auth/revoke-key
Content-Type: application/json

{
  "apiKey": "api-key-to-revoke"
}
```

## 💻 **Client Integration Examples**

### **JavaScript/Fetch**
```javascript
const apiKey = 'ci-solution-beta-key-2024';

fetch('http://localhost:5199/api/authoring', {
  headers: {
    'X-API-Key': apiKey,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

### **C# HttpClient**
```csharp
using var client = new HttpClient();
client.DefaultRequestHeaders.Add("X-API-Key", "ci-solution-beta-key-2024");

var response = await client.GetAsync("http://localhost:5199/api/authoring");
var content = await response.Content.ReadAsStringAsync();
```

### **Python Requests**
```python
import requests

headers = {
    'X-API-Key': 'ci-solution-beta-key-2024',
    'Content-Type': 'application/json'
}

response = requests.get('http://localhost:5199/api/authoring', headers=headers)
data = response.json()
```

### **Postman**
1. Create a new request
2. Add header: `X-API-Key` = `ci-solution-beta-key-2024`
3. Send the request

## 🧪 **Testing**

### **Test Valid API Key**
```bash
curl -H "X-API-Key: ci-solution-beta-key-2024" \
     http://localhost:5199/api/authoring
```

**Expected Response:** `200 OK` with authoring data

### **Test Missing API Key**
```bash
curl http://localhost:5199/api/authoring
```

**Expected Response:** `401 Unauthorized`
```json
{
  "success": false,
  "message": "Authentication required",
  "error": {
    "code": "UNAUTHORIZED",
    "message": "API key is required"
  }
}
```

### **Test Invalid API Key**
```bash
curl -H "X-API-Key: invalid-key" \
     http://localhost:5199/api/authoring
```

**Expected Response:** `401 Unauthorized`
```json
{
  "success": false,
  "message": "Authentication required",
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Invalid API key"
  }
}
```

### **Test Rate Limiting**
```bash
# Make 1001 requests quickly to test rate limiting
for i in {1..1001}; do
  curl -H "X-API-Key: ci-solution-beta-key-2024" \
       http://localhost:5199/api/authoring/health
done
```

**Expected Response after 1000 requests:** `429 Too Many Requests`

## 🔄 **Changing Your API Key**

### **Method 1: Update Configuration**
1. Edit `src/CIAPI/CIAPI.Web/appsettings.json`
2. Change `Authentication.ApiKey.MasterApiKey` value
3. Restart the application

### **Method 2: Generate New Key**
1. Use the `/api/auth/generate-key` endpoint
2. Update your clients to use the new key
3. Optionally revoke the old key

## 🚀 **Production Deployment**

### **Security Recommendations**
1. **Change the Master Key**: Use a strong, unique API key
2. **Enable HTTPS**: Set `RequireHttps: true`
3. **Adjust Rate Limits**: Set appropriate limits for your use case
4. **Monitor Usage**: Regularly check API key usage logs
5. **Key Rotation**: Implement regular key rotation

### **Environment Variables**
```bash
# Set via environment variables for production
export Authentication__ApiKey__MasterApiKey="your-production-key"
export Authentication__ApiKey__RequireHttps="true"
export Authentication__ApiKey__DefaultRateLimit="500"
```

## 📊 **Monitoring & Analytics**

### **Usage Tracking**
- All API requests are logged with timestamps
- Rate limit violations are tracked
- Client IP addresses are recorded
- User agent information is captured

### **Health Monitoring**
```bash
# Check API health
curl -H "X-API-Key: ci-solution-beta-key-2024" \
     http://localhost:5199/api/authoring/health
```

### **Rate Limit Monitoring**
Check response headers for rate limit information:
- `X-RateLimit-Limit`: Total requests allowed
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: When the limit resets

## 🎯 **Next Steps**

### **For Beta Testing**
1. ✅ Use the master key: `ci-solution-beta-key-2024`
2. ✅ Test all your API endpoints
3. ✅ Monitor rate limits and usage
4. ✅ Generate additional keys as needed

### **For Production**
1. 🔄 Change to a secure production API key
2. 🔄 Enable HTTPS requirement
3. 🔄 Implement database-backed key storage
4. 🔄 Add user authentication and authorization
5. 🔄 Set up monitoring and alerting

---

## 🎉 **You're Ready!**

Your CI Solution API is now protected with API Key authentication. Use the master key `ci-solution-beta-key-2024` to access all endpoints, and the system will handle rate limiting, usage tracking, and security automatically.

**Happy coding!** 🚀
