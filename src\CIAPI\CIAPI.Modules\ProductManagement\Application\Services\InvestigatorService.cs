using GDAPI.Modules.ProductManagement.Domain.Entities;
using GDAPI.Modules.ProductManagement.Domain.Interfaces;
using GDAPI.Modules.ProductManagement.Application.DTOs;
using GDAPI.Shared.DTOs.Common;
using Microsoft.Extensions.Logging;

namespace GDAPI.Modules.ProductManagement.Application.Services;

/// <summary>
/// Investigator service implementation
/// </summary>
public class InvestigatorService : IInvestigatorService
{
    private readonly IInvestigatorRepository _investigatorRepository;
    private readonly ILogger<InvestigatorService> _logger;

    public InvestigatorService(IInvestigatorRepository investigatorRepository, ILogger<InvestigatorService> logger)
    {
        _investigatorRepository = investigatorRepository ?? throw new ArgumentNullException(nameof(investigatorRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ApiResponse<PagedResult<InvestigatorDto>>> GetInvestigatorsSamplePagedAsync(InvestigatorSearchRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting paginated investigators using stored procedure. Page: {PageNumber}, Size: {PageSize}",
                request.PageNumber, request.PageSize);

            var (items, totalCount) = await _investigatorRepository.GetInvestigatorsSamplePagedAsync(
                request.PageNumber,
                request.PageSize,
                request.SearchTerm,
                request.RegionFilter,
                request.CountryFilter,
                cancellationToken);

            var dtos = items.Select(MapToDto);
            var pagedResult = new PagedResult<InvestigatorDto>(dtos, request.PageNumber, request.PageSize, totalCount);

            return ApiResponse<PagedResult<InvestigatorDto>>.SuccessResult(pagedResult, "Investigators retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting paginated investigators from stored procedure");
            return ApiResponse<PagedResult<InvestigatorDto>>.ErrorResult("An error occurred while retrieving investigators");
        }
    }



    private static InvestigatorDto MapToDto(Investigator investigator)
    {
        return new InvestigatorDto
        {
            Id = investigator.Id,
            InvestigatorName = investigator.InvestigatorName,
            SpecializationNames = investigator.SpecializationNames,
            Designation = investigator.Designation,
            Organisation = investigator.Organisation,
            ContactNumber = investigator.ContactNumber,
            EmailID = investigator.EmailID,
            Fax = investigator.Fax,
            RegionName = investigator.RegionName,
            CountryName = investigator.CountryName,
            StateName = investigator.StateName,
            CityName = investigator.CityName,
            IsActive = investigator.IsActive,
            CreatedAt = investigator.CreatedAt,
            UpdatedAt = investigator.UpdatedAt,
            FullLocation = investigator.FullLocation,
            ContactInfo = investigator.ContactInfo
        };
    }
}
