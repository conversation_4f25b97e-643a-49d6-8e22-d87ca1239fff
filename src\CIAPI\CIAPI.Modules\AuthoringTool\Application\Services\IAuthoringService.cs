using CIAPI.Modules.AuthoringTool.Application.DTOs;
using CIAPI.Modules.AuthoringTool.Domain.Entities;
using CIAPI.Shared.DTOs.Common;

namespace CIAPI.Modules.AuthoringTool.Application.Services;

/// <summary>
/// Authoring service interface for authoring management operations
/// </summary>
public interface IAuthoringService
{
    /// <summary>
    /// Get paginated authoring items using stored procedure with filtering support
    /// </summary>
    /// <param name="request">Search request with pagination and filter parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated result of authoring items</returns>
    Task<ApiResponse<PagedResult<AuthoringDto>>> GetAuthoringSamplePagedAsync(AuthoringSearchRequest request, CancellationToken cancellationToken = default);

    #region : Get User Details 

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves user details.

    /// <summary>
    /// Gets all users with optional type filter.
    /// </summary>
    /// <param name="type">User type filter (default: "All")</param>
    /// <returns>API response containing the list of users</returns>
    Task<ApiResponse<List<UserDetailsDto>>> GetAllUsersAsync(string type = "All");

    #endregion

    #region : Clinical Comparators

    // Added by: Avinash Veerella
    // Date: 16-Jul-2025
    // Description: Retrieves Clinical Comparators details.

    /// <summary>
    /// Gets clinical comparators grouped by indications.
    /// </summary>
    /// <returns>
    /// API response containing a collection of clinical comparators grouped by indications.
    /// </returns>
    Task<ApiResponse<IEnumerable<ClinicalComparatorsGroupByIndicationsDto>>> GetClinicalComparatorsAsync();

    #endregion

}
