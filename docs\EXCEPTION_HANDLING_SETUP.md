# Exception Handling - Quick Setup Guide

## 🚀 Quick Start

### Step 1: Run Database Setup
Execute the database schema script to create the ErrorLogs table and stored procedures:

```sql
-- Run this script in SQL Server Management Studio or Azure Data Studio
-- File: docs/error-logging-schema.sql
```

### Step 2: Test the System
The exception handling system is already integrated and ready to use!

## 🧪 Testing the Exception Handling

### 1. Start the Application
```bash
cd src/CIAPI/CIAPI.Web
dotnet run
```

### 2. Test Different Exception Types

#### Test Validation Exception
```bash
# Invalid page number (should return 400 with validation error)
curl -X GET "http://localhost:5199/api/authoring?pageNumber=0"
```

#### Test Not Found Exception
```bash
# This will work with existing data, but you can modify the service to throw NotFoundException for testing
curl -X GET "http://localhost:5199/api/authoring/999999"
```

#### Test Database Exception
```bash
# Stop SQL Server temporarily to test database connection errors
curl -X GET "http://localhost:5199/api/authoring"
```

### 3. Check Error Logs

#### File Logs
Check the `Logs/errors-{date}.log` file in the application directory:

```bash
# View today's error log
cat Logs/errors-2024-01-20.log
```

#### Database Logs
Query the ErrorLogs table:

```sql
-- View recent errors
SELECT TOP 10 
    ErrorCode, 
    Message, 
    Module, 
    CorrelationId, 
    CreatedAt 
FROM ErrorLogs 
ORDER BY CreatedAt DESC;

-- View errors by module
SELECT * FROM ErrorLogs 
WHERE Module = 'AuthoringTool' 
ORDER BY CreatedAt DESC;
```

## 📋 Expected Response Formats

### Validation Error (400 Bad Request)
```json
{
  "success": false,
  "message": "Page number must be greater than 0",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Page number must be greater than 0",
    "correlationId": "abc-123-def-456",
    "module": "AuthoringTool",
    "timestamp": "2024-01-20T15:45:30Z"
  },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

### Not Found Error (404 Not Found)
```json
{
  "success": false,
  "message": "Authoring item with ID '999999' was not found",
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "Authoring item with ID '999999' was not found",
    "details": {
      "resourceType": "Authoring",
      "resourceId": "999999"
    },
    "correlationId": "abc-123-def-456",
    "module": "AuthoringTool",
    "timestamp": "2024-01-20T15:45:30Z"
  },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

### Database Error (500 Internal Server Error)
```json
{
  "success": false,
  "message": "An unexpected error occurred while retrieving authoring items",
  "error": {
    "code": "DATABASE_ERROR",
    "message": "Failed to retrieve authoring items from database",
    "correlationId": "abc-123-def-456",
    "module": "AuthoringTool",
    "timestamp": "2024-01-20T15:45:30Z"
  },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

## 🔧 Configuration Options

### appsettings.json
```json
{
  "Logging": {
    "ErrorLogDirectory": "Logs",
    "LogLevel": {
      "Default": "Information",
      "CIAPI.Shared.Middleware.GlobalExceptionMiddleware": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CISolution;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

### Environment Variables
```bash
# Set log directory
export Logging__ErrorLogDirectory="/var/logs/ciapi"

# Set connection string
export ConnectionStrings__DefaultConnection="your-connection-string"
```

## 🎯 Adding Custom Exceptions to Your Modules

### 1. In Your Service Layer
```csharp
using CIAPI.Shared.Exceptions;

public async Task<AuthoringDto> GetAuthoringByIdAsync(int id)
{
    if (id <= 0)
        throw new ValidationException("ID must be greater than 0")
            .SetModule("AuthoringTool");

    var authoring = await _repository.GetByIdAsync(id);
    
    if (authoring == null)
        throw NotFoundException.ForResource("Authoring", id.ToString())
            .SetModule("AuthoringTool");

    return MapToDto(authoring);
}
```

### 2. In Your Controller
```csharp
using CIAPI.Shared.Exceptions;

[HttpGet("{id}")]
public async Task<ActionResult<ApiResponse<AuthoringDto>>> GetAuthoring(int id)
{
    // No try-catch needed - middleware handles exceptions
    var result = await _authoringService.GetAuthoringByIdAsync(id);
    return Ok(ApiResponse<AuthoringDto>.SuccessResult(result));
}
```

### 3. Available Exception Types
- `ValidationException` - For input validation errors (400)
- `BusinessException` - For business rule violations (400)
- `NotFoundException` - For missing resources (404)
- `UnauthorizedException` - For authentication errors (401)
- `ForbiddenException` - For authorization errors (403)
- `DatabaseException` - For database errors (500)
- `DatabaseConnectionException` - For connection errors (500)
- `DatabaseTimeoutException` - For timeout errors (500)

## 🔍 Monitoring and Troubleshooting

### Check Application Logs
```bash
# View application logs
dotnet run --verbosity normal

# View with specific log level
dotnet run --verbosity detailed
```

### Check Error Log Files
```bash
# View error logs
tail -f Logs/errors-$(date +%Y-%m-%d).log

# Search for specific errors
grep "VALIDATION_ERROR" Logs/errors-*.log
```

### Database Monitoring
```sql
-- Monitor error frequency
SELECT 
    ErrorCode,
    COUNT(*) as ErrorCount,
    MAX(CreatedAt) as LastOccurrence
FROM ErrorLogs 
WHERE CreatedAt >= DATEADD(HOUR, -24, GETUTCDATE())
GROUP BY ErrorCode
ORDER BY ErrorCount DESC;

-- Monitor by module
SELECT 
    Module,
    COUNT(*) as ErrorCount
FROM ErrorLogs 
WHERE CreatedAt >= DATEADD(HOUR, -24, GETUTCDATE())
GROUP BY Module
ORDER BY ErrorCount DESC;
```

## ✅ Verification Checklist

- [ ] Database schema created successfully
- [ ] Application starts without errors
- [ ] Error logs are created in the Logs directory
- [ ] Database logging is working (check ErrorLogs table)
- [ ] API returns consistent error responses
- [ ] Correlation IDs are present in responses
- [ ] Stack traces only appear in development environment
- [ ] File and database logs contain the same information

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check connection string in appsettings.json
   - Ensure SQL Server is running
   - Verify database permissions

2. **File Logging Not Working**
   - Check write permissions on Logs directory
   - Verify ErrorLogDirectory configuration
   - Check disk space

3. **Middleware Not Catching Exceptions**
   - Ensure middleware is registered first in pipeline
   - Check that services are registered correctly
   - Verify exception types inherit from BaseException

### Getting Help
- Check the main documentation: `docs/GLOBAL_EXCEPTION_HANDLING.md`
- Review error logs for detailed information
- Use correlation IDs to track specific requests

---

**🎉 You're all set!** The global exception handling system is now ready to provide consistent, reliable error handling across your entire application.
