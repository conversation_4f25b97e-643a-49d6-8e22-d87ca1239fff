{"version": 2, "dgSpecHash": "BbyBbsFEiO4=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.openapi\\9.0.6\\microsoft.aspnetcore.openapi.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\9.0.0\\microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.6.23\\microsoft.openapi.1.6.23.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\9.0.3\\swashbuckle.aspnetcore.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\9.0.3\\swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\9.0.3\\swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\9.0.3\\swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512"], "logs": [{"code": "NU1104", "level": "Error", "message": "Unable to find project 'C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\ProductManagement\\GDAPI.Modules.ProductManagement.csproj'. Check that the project reference is valid and that the project file exists.", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "libraryId": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\ProductManagement\\GDAPI.Modules.ProductManagement.csproj", "targetGraphs": ["net9.0"]}, {"code": "NU1104", "level": "Error", "message": "Unable to find project 'C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Shared\\GDAPI.Shared.csproj'. Check that the project reference is valid and that the project file exists.", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "libraryId": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Shared\\GDAPI.Shared.csproj", "targetGraphs": ["net9.0"]}, {"code": "NU1104", "level": "Error", "message": "Unable to find project 'C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj'. Check that the project reference is valid and that the project file exists.", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "libraryId": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj", "targetGraphs": ["net9.0"]}, {"code": "NU1104", "level": "Error", "message": "Unable to find project 'C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj'. Check that the project reference is valid and that the project file exists.", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "libraryId": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj", "targetGraphs": ["net9.0"]}]}