using System.Net;

namespace GDAPI.Shared.Exceptions;

/// <summary>
/// Exception for when a requested resource is not found
/// </summary>
public class NotFoundException : BaseException
{
    public override HttpStatusCode StatusCode => HttpStatusCode.NotFound;
    public override string ErrorCode => "RESOURCE_NOT_FOUND";

    /// <summary>
    /// The type of resource that was not found
    /// </summary>
    public string? ResourceType { get; set; }
    
    /// <summary>
    /// The identifier that was used to search for the resource
    /// </summary>
    public string? ResourceId { get; set; }

    public NotFoundException(string message) : base(message)
    {
    }

    public NotFoundException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public NotFoundException(string message, string correlationId) : base(message, correlationId)
    {
    }

    public static NotFoundException ForResource(string resourceType, string resourceId)
    {
        var exception = new NotFoundException($"{resourceType} with ID '{resourceId}' was not found");
        exception.ResourceType = resourceType;
        exception.ResourceId = resourceId;
        exception.Details = new { ResourceType = resourceType, ResourceId = resourceId };
        return exception;
    }

    public static NotFoundException ForResource(string resourceType, string resourceId, string correlationId)
    {
        var exception = new NotFoundException($"{resourceType} with ID '{resourceId}' was not found", correlationId);
        exception.ResourceType = resourceType;
        exception.ResourceId = resourceId;
        exception.Details = new { ResourceType = resourceType, ResourceId = resourceId };
        return exception;
    }

    /// <summary>
    /// Set the resource type and ID
    /// </summary>
    public NotFoundException SetResource(string resourceType, string resourceId)
    {
        ResourceType = resourceType;
        ResourceId = resourceId;
        Details = new { ResourceType = resourceType, ResourceId = resourceId };
        return this;
    }
}
