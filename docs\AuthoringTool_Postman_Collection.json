{"info": {"_postman_id": "a8f5e3c7-6d2b-4e9a-8f1c-d5e9b7c8a6f5", "name": "AuthoringTool API", "description": "Collection for testing the AuthoringTool module API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Authoring Items", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/authoring", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "authoring"]}, "description": "Get all authoring items with default pagination (10 items per page)"}, "response": []}, {"name": "Get Authoring Items (Paginated)", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/authoring?pageNumber=1&pageSize=5", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "authoring"], "query": [{"key": "pageNumber", "value": "1"}, {"key": "pageSize", "value": "5"}]}, "description": "Get authoring items with custom pagination (5 items per page)"}, "response": []}, {"name": "Search Documents", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?searchTerm=protocol", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "searchTerm", "value": "protocol"}]}, "description": "Search documents by title or description"}, "response": []}, {"name": "Filter by Status (Draft)", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?statusFilter=Draft", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "statusFilter", "value": "Draft"}]}, "description": "Get all draft documents"}, "response": []}, {"name": "Filter by Status (InReview)", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?statusFilter=InReview", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "statusFilter", "value": "InReview"}]}, "description": "Get all documents in review"}, "response": []}, {"name": "Filter by Status (Approved)", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?statusFilter=Approved", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "statusFilter", "value": "Approved"}]}, "description": "Get all approved documents"}, "response": []}, {"name": "Filter by Status (Published)", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?statusFilter=Published", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "statusFilter", "value": "Published"}]}, "description": "Get all published documents"}, "response": []}, {"name": "Filter by Department", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?departmentFilter=Clinical Research", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "<PERSON><PERSON><PERSON>er", "value": "Clinical Research"}]}, "description": "Get documents from Clinical Research department"}, "response": []}, {"name": "Filter by Category", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?categoryFilter=Clinical", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "categoryFilter", "value": "Clinical"}]}, "description": "Get documents in Clinical category"}, "response": []}, {"name": "Filter by Author", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?authorFilter=Dr. <PERSON>", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "Dr. <PERSON>"}]}, "description": "Get documents by a specific author"}, "response": []}, {"name": "Combined Filters", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?statusFilter=Draft&departmentFilter=Clinical Research", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "statusFilter", "value": "Draft"}, {"key": "<PERSON><PERSON><PERSON>er", "value": "Clinical Research"}]}, "description": "Get draft documents from Clinical Research department"}, "response": []}, {"name": "Invalid Page Number", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?pageNumber=0", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "pageNumber", "value": "0"}]}, "description": "Test error handling with invalid page number"}, "response": []}, {"name": "Invalid <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/documents?pageSize=101", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "documents"], "query": [{"key": "pageSize", "value": "101"}]}, "description": "Test error handling with invalid page size"}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:5199/api/authoring/health", "protocol": "http", "host": ["localhost"], "port": "5199", "path": ["api", "authoring", "health"]}, "description": "Check the health of the authoring module"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:5199", "type": "string"}]}