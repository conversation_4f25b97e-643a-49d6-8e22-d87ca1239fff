using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using CIAPI.Shared.Authentication.Models;
using CIAPI.Shared.Authentication.Services;

namespace CIAPI.Shared.Authentication.Middleware;

/// <summary>
/// Extension methods for configuring API key authentication
/// </summary>
public static class AuthenticationMiddlewareExtensions
{
    /// <summary>
    /// Add API key authentication services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Optional configuration action</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddApiKeyAuthentication(this IServiceCollection services, Action<ApiKeySettings>? configureOptions = null)
    {
        // Configure API key settings
        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }
        else
        {
            services.AddOptions<ApiKeySettings>()
                .Configure<IConfiguration>((settings, configuration) =>
                {
                    configuration.GetSection(ApiKeySettings.SectionName).Bind(settings);
                });
        }

        // Register API key service (using in-memory implementation for now)
        services.AddSingleton<IApiKeyService, InMemoryApiKeyService>();

        return services;
    }

    /// <summary>
    /// Use API key authentication middleware
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder for chaining</returns>
    public static IApplicationBuilder UseApiKeyAuthentication(this IApplicationBuilder app)
    {
        return app.UseMiddleware<ApiKeyAuthenticationMiddleware>();
    }
}

/// <summary>
/// Extension methods for working with API key context
/// </summary>
public static class ApiKeyContextExtensions
{
    /// <summary>
    /// Get the API key information from the current HTTP context
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>API key information or null if not authenticated</returns>
    public static ApiKeyInfo? GetApiKeyInfo(this Microsoft.AspNetCore.Http.HttpContext context)
    {
        return context.Items["ApiKeyInfo"] as ApiKeyInfo;
    }

    /// <summary>
    /// Get the remaining requests for the current API key
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>Remaining requests or null if not available</returns>
    public static int? GetRemainingRequests(this Microsoft.AspNetCore.Http.HttpContext context)
    {
        return context.Items["RemainingRequests"] as int?;
    }

    /// <summary>
    /// Check if the current request is authenticated with an API key
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>True if authenticated with API key</returns>
    public static bool IsApiKeyAuthenticated(this Microsoft.AspNetCore.Http.HttpContext context)
    {
        return context.GetApiKeyInfo() != null;
    }

    /// <summary>
    /// Get the client name from the current API key
    /// </summary>
    /// <param name="context">HTTP context</param>
    /// <returns>Client name or null if not authenticated</returns>
    public static string? GetApiKeyClientName(this Microsoft.AspNetCore.Http.HttpContext context)
    {
        return context.GetApiKeyInfo()?.ClientName;
    }
}
