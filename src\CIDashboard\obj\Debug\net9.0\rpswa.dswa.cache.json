{"GlobalPropertiesHash": "JuLWWJUeRJ9mojBs0SWT/h9LLxkYgTXKmJy/KNS3x+g=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["YhU/024lK/MmDSnllCtiMfz6hdFjPbnBil1dpji4Iz4=", "YK6vHcZxC8jbr5d+S/ovnyEf0A1Vc9CxoUMk1MB4bpY=", "959EU85/3/2H5KDbRcZeNjC8KgBo5KJiu7joQhngXFo=", "/vO/IhSf3s2EoO5VN9OiJ91AJT9bjiHAfKgnWvkSE6Y=", "jGoMLpqy7oha+fhrFxAYFIaBJU98Wp/rpxyvFA4htik=", "YIwtQnXoK98oLMjP6A3/Lf1eZqZo/62a2Lu6AjfkdsI=", "JTgxBxd0jl1PbZbQhGYeTtQce+gMs19F+Dx1Y0Nz3Fo=", "yHTBX6s4Tet0WuD0bv1ftYIRj/mZVcOW18MFKjk+MZM=", "nECVd4g8w77McsWSoKztGLJK7Z5VMVRl7YpAU59v3Hc=", "kyiPmA/g0vefDuMBO2EJdRqFERGO5IpY3qdgTJX9m1w=", "qDUcBkNJp7YLD7xu2c9TkRyxrpvetatUswLbZLHtvGk=", "YJ4bwk2GCG9u8+G8Ou8pdAubDuNRC9SM/hKglZmeAZg=", "Dtv6UYiMnbwdpCkWSBxZBHuMxbc0i0zHRCi3aTJl+bs=", "zWX9Yhd8suBYT2F9o4kKqP8moROJapjiiaV/9SEjbtI=", "oj6hMpVPO7/phP0Z6jnJJ9aPfQIiV8I7HcOg0tc6RVY=", "A1NZwjpljcj2TewPKi1Wy265v1eRCgL8yoMPIykOcVY=", "188PBne1PM4Gclao2/D26Lp69Bbgrs2zuLLM05D8o1Y=", "LEcgiInsncfibXf+gRRE8z5Qs/WY3v87pvHGj1U7TTY=", "SXcxjFrMGg9i2F+J6pqBtEexSBQVn5jU/g0ZkPtCn7s=", "4S9optj5rG+Q1AisitLV2+XCwMJfU0tmEQ7BImGO04o=", "DPvvS8LDseImyPAHqWX0FM8T4nxsMx9TasYBbmwKoU8=", "wPzvQy1Uwe3g0+L45uFvrmPjVA4J+c9+qQ8npG2rqcY=", "I4JC61b3PtIlS/8VTwM1h8c3RnKlFTPAjSVyOE1hhy8=", "nNl38+v9ZBuaEflhEWiptLxQGoRF2ylSu2fjnXc0fx0=", "WyXJGr102vu65q4d0tuGACdGHS4+icYaJKkKySeGB5w=", "R53dKChL8S3LZTcn8LMbluPhYBLf64Q0UP/3/8CPO08=", "JMR33QX4iWClEM/1dpAfW6GK4Lm2++yL7AVr65JS68A=", "lF37FKPORJA3COze6MA3huohJ6fl2+ogAZ2CnCJf55w=", "f2D09IXdicmdsDBhKVfamHTNclVmej31QS2TrWHSh94=", "JUZFgcUsHsq66ffs3KSQZWLVIBWPW4Ey0mCTa0CCUxs=", "6K8+PQX6CQmPuEQiwTMLlV5m7JGT64r6nMlWvQzNB8s=", "HV3371M/VPowT7UrJQhvdEsq+3C6SKovaQmzrVf5aqA=", "/WED48PBk2/TpM/7M/6G/TmCBUC0HluOkYysEawj1bI=", "ToieVLDVA65IS5Ubv6OUjBeaxElfU0cGPRxjkXBi/3s=", "CBJnwPuY63mD+BrGfKW55IImRdXNb17194eDYJ7kl70=", "wmU+iH8mBpO7O9+7KQhdzRw7+8OGIpIYc2V0nOIrB7A=", "coGvVkCTeiScudgrPPByku694JurhAbfJE5QcnJYJ8c=", "oIzMQzkXf1h3uUGNLRYi9xrk9QnfYe2+iNAGmK8HqYA=", "OroWNqGFwXsalZBxtwT/dUYHdh9ywY0p1FprfKq8Izw=", "qyss25ue7x3ydYKfKaMF5gTC/WntSSt1GoqsJZ3i8Sw=", "biZLYWtXg5UnglF5G+6PhULhgKZJ/ZVAU8AtgzOuRG0=", "DLjykvpYLU0p+a+v8bWb5ZiF8apF5BRWBQnpbwMytng=", "hGSNP/quev22rddVyxg71aaOmZmxeNuLQzR8JxobhFk=", "9/4n5IStWi6XPJSdFxDsOPEywHSmW1rUqBBQp/CeXv8=", "PftwENq2jqRcgabvNubBYAyV7MWzeOA3CUjMxJ/bKmw=", "HLtPWduHuZWwNUAiVFoyyyb4Hf+1z+nFIo3YlblicVg=", "FEAjdJZayL9v0D3Z8aWd/EcmB/0ry0f5fSzQ92nCpKY=", "+YPPJNvronaPAPWogOEzVSEhSe3sALK6C8yOtozg9Js=", "b7ycoRTIp+5FUVmrCHYXXQIrYA6/FC9ymVm0GXmF4y4=", "pgrVTlbiREUeyjc0JIcihuvcef2FCUkjzt/r3/FwldY=", "9Elq7RBmCjojU/hlDoMUCrQEU5TtPBkm5jdND2QTNV0=", "rCw6/AirDm0gIAL3DoNtuQ4XFFbv12YzsU/pp7kCoG0=", "28zpiEiOkToz/+HzttOgzgE5wuSAq/L6ZpKP1X9PJG0=", "aYJlcdT8TyC9aVsDEWHj+j0ngCjP3sCrrkFW63DLWME=", "5ORVMM+YT1D9xj3SLEFR4ik0qh5rSxqQu6FfFpJXMqQ=", "6uxM8xG6zu3B3qJtEj6yu6mJmRTJu2O62tjDisSxE+A=", "w/w9084Yo6PkJI3VLWmzcdF9CvftqNGg8fbFwU5i9Pg=", "CfPDixMbQ5RZgheTGL6jrz6dTjN+L96LC1jz9621sc0=", "e0096EDT8Or6Q8u/b3R3atrQPAZ0xpYBFmZcjKswSfo=", "ZcrtL0sNzriLX6Wiu4HMW5BVZAUIoNBSnoGpEqR9YI0=", "un7yJmNTM9PmWNbvYgFmLcPYR+8zmUaElSgBH8ZovMg=", "y6x/NQslzEdPbz4XGjP42pWWGlKqyprgTsNvFVeC/VY=", "CeeToeGRLrS1wNxL9z1VM3K477FOI66Jdkj8vSFq8xk=", "IoHvXNqDCx8eIreDLn5eCFIJq5O4Z2XOBTs94XKqXDM=", "1hLuH5gdqJIqMR/qS4vy6Dj8A9+VtqMBvtyC0SYMdrk=", "sBhy5GnbcL0mWq029LnCoDqXR+wKohH10P5Mdopr4ak=", "nkR8PP9rXOEgbn1SBU0mkMQtAEnhM2w1egM7Jq9dS+w=", "UNzvvCdD3WhxLx50KYRzHQpL+JkQ5SdBEqRK0y+8nhA=", "p2AzuaB9lE8V7Ga5DJW1FZY3Rkbq4QjJT6j5uWl6i8E=", "P8P2g4gYEh4fk7HQQhy68/eo2pw/+dbBHRX8AqDuqnk=", "JBxFoQtiY/omFxZYkZeOVWNH8b5XpaqKcqacV2Eo9a8=", "bNKNDccU/rY7Jt65JT47phwu3eq28B36Qinkx8l0bSs=", "/exI/lGhiwX+dF40ZM28DuHV13iPXi2uf5V3CqJJyKo="], "CachedAssets": {"CeeToeGRLrS1wNxL9z1VM3K477FOI66Jdkj8vSFq8xk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-11T07:40:36.5607067+00:00"}, "y6x/NQslzEdPbz4XGjP42pWWGlKqyprgTsNvFVeC/VY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-11T07:40:36.6141128+00:00"}, "un7yJmNTM9PmWNbvYgFmLcPYR+8zmUaElSgBH8ZovMg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-11T07:40:36.6096988+00:00"}, "ZcrtL0sNzriLX6Wiu4HMW5BVZAUIoNBSnoGpEqR9YI0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-11T07:40:36.6039279+00:00"}, "e0096EDT8Or6Q8u/b3R3atrQPAZ0xpYBFmZcjKswSfo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-11T07:40:36.5886467+00:00"}, "CfPDixMbQ5RZgheTGL6jrz6dTjN+L96LC1jz9621sc0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-11T07:40:36.5886467+00:00"}, "w/w9084Yo6PkJI3VLWmzcdF9CvftqNGg8fbFwU5i9Pg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-11T07:40:36.5786336+00:00"}, "6uxM8xG6zu3B3qJtEj6yu6mJmRTJu2O62tjDisSxE+A=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-11T07:40:36.5607067+00:00"}, "5ORVMM+YT1D9xj3SLEFR4ik0qh5rSxqQu6FfFpJXMqQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-11T07:40:36.6205032+00:00"}, "aYJlcdT8TyC9aVsDEWHj+j0ngCjP3sCrrkFW63DLWME=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-11T07:40:36.617763+00:00"}, "28zpiEiOkToz/+HzttOgzgE5wuSAq/L6ZpKP1X9PJG0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-11T07:40:36.617763+00:00"}, "rCw6/AirDm0gIAL3DoNtuQ4XFFbv12YzsU/pp7kCoG0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-11T07:40:36.6161201+00:00"}, "9Elq7RBmCjojU/hlDoMUCrQEU5TtPBkm5jdND2QTNV0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-11T07:40:36.5674009+00:00"}, "pgrVTlbiREUeyjc0JIcihuvcef2FCUkjzt/r3/FwldY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-11T07:40:36.6320123+00:00"}, "b7ycoRTIp+5FUVmrCHYXXQIrYA6/FC9ymVm0GXmF4y4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-11T07:40:36.6205032+00:00"}, "+YPPJNvronaPAPWogOEzVSEhSe3sALK6C8yOtozg9Js=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-11T07:40:36.5526989+00:00"}, "FEAjdJZayL9v0D3Z8aWd/EcmB/0ry0f5fSzQ92nCpKY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-11T07:40:36.5786336+00:00"}, "HLtPWduHuZWwNUAiVFoyyyb4Hf+1z+nFIo3YlblicVg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-11T07:40:36.5771807+00:00"}, "PftwENq2jqRcgabvNubBYAyV7MWzeOA3CUjMxJ/bKmw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-11T07:40:36.5737127+00:00"}, "9/4n5IStWi6XPJSdFxDsOPEywHSmW1rUqBBQp/CeXv8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-11T07:40:36.5737127+00:00"}, "hGSNP/quev22rddVyxg71aaOmZmxeNuLQzR8JxobhFk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-11T07:40:36.5684895+00:00"}, "DLjykvpYLU0p+a+v8bWb5ZiF8apF5BRWBQnpbwMytng=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-11T07:40:36.5684895+00:00"}, "biZLYWtXg5UnglF5G+6PhULhgKZJ/ZVAU8AtgzOuRG0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-11T07:40:36.5684895+00:00"}, "qyss25ue7x3ydYKfKaMF5gTC/WntSSt1GoqsJZ3i8Sw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-11T07:40:36.5607067+00:00"}, "OroWNqGFwXsalZBxtwT/dUYHdh9ywY0p1FprfKq8Izw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-11T07:40:36.5607067+00:00"}, "oIzMQzkXf1h3uUGNLRYi9xrk9QnfYe2+iNAGmK8HqYA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-11T07:40:36.5526989+00:00"}, "coGvVkCTeiScudgrPPByku694JurhAbfJE5QcnJYJ8c=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-11T07:40:36.5526989+00:00"}, "wmU+iH8mBpO7O9+7KQhdzRw7+8OGIpIYc2V0nOIrB7A=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-11T07:40:36.5526989+00:00"}, "CBJnwPuY63mD+BrGfKW55IImRdXNb17194eDYJ7kl70=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-11T07:40:36.5526989+00:00"}, "ToieVLDVA65IS5Ubv6OUjBeaxElfU0cGPRxjkXBi/3s=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-11T07:40:36.5509273+00:00"}, "/WED48PBk2/TpM/7M/6G/TmCBUC0HluOkYysEawj1bI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-11T07:40:36.5509273+00:00"}, "HV3371M/VPowT7UrJQhvdEsq+3C6SKovaQmzrVf5aqA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-11T07:40:36.5469755+00:00"}, "6K8+PQX6CQmPuEQiwTMLlV5m7JGT64r6nMlWvQzNB8s=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-11T07:40:36.5469755+00:00"}, "JUZFgcUsHsq66ffs3KSQZWLVIBWPW4Ey0mCTa0CCUxs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-11T07:40:36.5469755+00:00"}, "f2D09IXdicmdsDBhKVfamHTNclVmej31QS2TrWHSh94=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-11T07:40:36.5450394+00:00"}, "lF37FKPORJA3COze6MA3huohJ6fl2+ogAZ2CnCJf55w=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-11T07:40:36.5435276+00:00"}, "JMR33QX4iWClEM/1dpAfW6GK4Lm2++yL7AVr65JS68A=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-11T07:40:36.5400944+00:00"}, "R53dKChL8S3LZTcn8LMbluPhYBLf64Q0UP/3/8CPO08=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-11T07:40:36.5400944+00:00"}, "WyXJGr102vu65q4d0tuGACdGHS4+icYaJKkKySeGB5w=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-11T07:40:36.5400944+00:00"}, "nNl38+v9ZBuaEflhEWiptLxQGoRF2ylSu2fjnXc0fx0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-11T07:40:36.537648+00:00"}, "I4JC61b3PtIlS/8VTwM1h8c3RnKlFTPAjSVyOE1hhy8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-11T07:40:36.537648+00:00"}, "wPzvQy1Uwe3g0+L45uFvrmPjVA4J+c9+qQ8npG2rqcY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-11T07:40:36.5344831+00:00"}, "DPvvS8LDseImyPAHqWX0FM8T4nxsMx9TasYBbmwKoU8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-11T07:40:36.5344831+00:00"}, "4S9optj5rG+Q1AisitLV2+XCwMJfU0tmEQ7BImGO04o=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-11T07:40:36.5344831+00:00"}, "SXcxjFrMGg9i2F+J6pqBtEexSBQVn5jU/g0ZkPtCn7s=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-11T07:40:36.5324519+00:00"}, "LEcgiInsncfibXf+gRRE8z5Qs/WY3v87pvHGj1U7TTY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-11T07:40:36.5315053+00:00"}, "188PBne1PM4Gclao2/D26Lp69Bbgrs2zuLLM05D8o1Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-11T07:40:36.5305182+00:00"}, "A1NZwjpljcj2TewPKi1Wy265v1eRCgL8yoMPIykOcVY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-11T07:40:36.5293936+00:00"}, "oj6hMpVPO7/phP0Z6jnJJ9aPfQIiV8I7HcOg0tc6RVY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-11T07:40:36.5293936+00:00"}, "zWX9Yhd8suBYT2F9o4kKqP8moROJapjiiaV/9SEjbtI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-11T07:40:36.5278838+00:00"}, "Dtv6UYiMnbwdpCkWSBxZBHuMxbc0i0zHRCi3aTJl+bs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-11T07:40:36.5278838+00:00"}, "YJ4bwk2GCG9u8+G8Ou8pdAubDuNRC9SM/hKglZmeAZg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-11T07:40:36.523365+00:00"}, "qDUcBkNJp7YLD7xu2c9TkRyxrpvetatUswLbZLHtvGk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-11T07:40:36.523365+00:00"}, "kyiPmA/g0vefDuMBO2EJdRqFERGO5IpY3qdgTJX9m1w=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-11T07:40:36.523365+00:00"}, "nECVd4g8w77McsWSoKztGLJK7Z5VMVRl7YpAU59v3Hc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-11T07:40:36.523365+00:00"}, "yHTBX6s4Tet0WuD0bv1ftYIRj/mZVcOW18MFKjk+MZM=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-11T07:40:36.523365+00:00"}, "JTgxBxd0jl1PbZbQhGYeTtQce+gMs19F+Dx1Y0Nz3Fo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-11T07:40:36.5178644+00:00"}, "YIwtQnXoK98oLMjP6A3/Lf1eZqZo/62a2Lu6AjfkdsI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-11T07:40:36.5178644+00:00"}, "jGoMLpqy7oha+fhrFxAYFIaBJU98Wp/rpxyvFA4htik=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-11T07:40:36.5136499+00:00"}, "/vO/IhSf3s2EoO5VN9OiJ91AJT9bjiHAfKgnWvkSE6Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-11T07:40:36.5124431+00:00"}, "959EU85/3/2H5KDbRcZeNjC8KgBo5KJiu7joQhngXFo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\js\\site.js", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-11T07:40:36.6506601+00:00"}, "YK6vHcZxC8jbr5d+S/ovnyEf0A1Vc9CxoUMk1MB4bpY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\favicon.ico", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-11T07:40:36.616709+00:00"}, "YhU/024lK/MmDSnllCtiMfz6hdFjPbnBil1dpji4Iz4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\css\\site.css", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\", "BasePath": "_content/CIDashboard", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-11T07:40:36.6506601+00:00"}}, "CachedCopyCandidates": {}}