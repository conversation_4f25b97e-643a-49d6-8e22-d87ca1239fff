// Global Exception Handling
using CIAPI.Shared.Middleware;

// API Key Authentication
using CIAPI.Shared.Authentication.Middleware;

// ProductManagement Module
using CIAPI.Modules.ProductManagement.Application.Services;
using CIAPI.Modules.ProductManagement.Domain.Interfaces;
using CIAPI.Modules.ProductManagement.Infrastructure.Data;
using CIAPI.Modules.ProductManagement.Infrastructure.Repositories;

// AuthoringTool Module
using CIAPI.Modules.AuthoringTool.Application.Services;
using CIAPI.Modules.AuthoringTool.Domain.Interfaces;
using CIAPI.Modules.AuthoringTool.Infrastructure.Data;
using CIAPI.Modules.AuthoringTool.Infrastructure.Repositories;

// UserManagement Module (placeholder for future implementation)
// using CIAPI.Modules.UserManagement.Application.Services;

using Microsoft.OpenApi.Models;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();

// Configure OpenAPI/Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "CI API",
        Version = "v1",
        Description = "Centralized API for CI Solution - A Modern Modular Monolithic Architecture",
        Contact = new OpenApiContact
        {
            Name = "CI Solution Team",
            Email = "<EMAIL>"
        }
    });

    // Add API Key Authentication to Swagger
    c.AddSecurityDefinition("ApiKey", new OpenApiSecurityScheme
    {
        Description = "API Key authentication. Add your API key in the X-API-Key header. Example: \"X-API-Key: your-api-key\"",
        Name = "X-API-Key",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "ApiKey"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "ApiKey"
                }
            },
            new string[] {}
        }
    });
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Register Global Exception Handling Services
builder.Services.AddGlobalExceptionHandling();

// Register API Key Authentication Services
builder.Services.AddApiKeyAuthentication();

// Register ProductManagement Module Services
builder.Services.AddScoped<IProductManagementDbConnectionFactory, ProductManagementSqlServerConnectionFactory>();
builder.Services.AddScoped<IInvestigatorRepository, InvestigatorRepository>();
builder.Services.AddScoped<IInvestigatorService, InvestigatorService>();

// Register AuthoringTool Module Services
builder.Services.AddScoped<IAuthoringToolDbConnectionFactory, AuthoringToolSqlServerConnectionFactory>();
builder.Services.AddScoped<IAuthoringRepository, AuthoringRepository>();
builder.Services.AddScoped<IAuthoringService, AuthoringService>();

// Register UserManagement Module Services (placeholder for future implementation)
// builder.Services.AddScoped<IUserManagementDbConnectionFactory, UserManagementSqlServerConnectionFactory>();
// builder.Services.AddScoped<IUserRepository, UserRepository>();
// builder.Services.AddScoped<IUserService, UserService>();

var app = builder.Build();

// Configure the HTTP request pipeline

// Global Exception Handling (should be first in pipeline)
app.UseGlobalExceptionHandling();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "CI API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// API Key Authentication (before controllers)
app.UseApiKeyAuthentication();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Health check endpoint
app.MapGet("/health", () => new { Status = "Healthy", Timestamp = DateTime.UtcNow })
   .WithName("HealthCheck")
   .WithTags("Health");

app.Run();
