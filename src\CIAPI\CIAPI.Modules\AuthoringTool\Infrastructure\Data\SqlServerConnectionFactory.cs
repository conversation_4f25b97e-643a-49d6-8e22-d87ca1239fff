using System.Data;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace CIAPI.Modules.AuthoringTool.Infrastructure.Data;

/// <summary>
/// SQL Server implementation of database connection factory for AuthoringTool module
/// </summary>
public class AuthoringToolSqlServerConnectionFactory : IAuthoringToolDbConnectionFactory
{
    private readonly string _connectionString;

    public AuthoringToolSqlServerConnectionFactory(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("CIUploadDatabaseConnection") 
            ?? throw new InvalidOperationException("DefaultConnection connection string is not configured");
    }

    public AuthoringToolSqlServerConnectionFactory(string connectionString)
    {
        _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
    }

    public string ConnectionString => _connectionString;

    public IDbConnection CreateConnection()
    {
        return new SqlConnection(_connectionString);
    }

    public async Task<IDbConnection> CreateConnectionAsync()
    {
        var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        return connection;
    }
}
