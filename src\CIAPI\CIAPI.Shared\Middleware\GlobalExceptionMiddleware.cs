using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using CIAPI.Shared.DTOs.Common;
using CIAPI.Shared.Exceptions;
using CIAPI.Shared.Services;

namespace CIAPI.Shared.Middleware;

/// <summary>
/// Global exception handling middleware for consistent error responses
/// </summary>
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;
    private readonly IHostEnvironment _environment;

    public GlobalExceptionMiddleware(
        RequestDelegate next,
        ILogger<GlobalExceptionMiddleware> logger,
        IHostEnvironment environment)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environment = environment ?? throw new ArgumentNullException(nameof(environment));
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception exception)
        {
            await HandleExceptionAsync(context, exception);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var correlationId = GetOrCreateCorrelationId(context);
        
        try
        {
            // Create error context
            var errorContext = CreateErrorContext(context, correlationId);

            // Log the exception asynchronously (don't await to avoid blocking the response)
            _ = Task.Run(async () =>
            {
                try
                {
                    // Resolve the error logging service from the request scope
                    var errorLoggingService = context.RequestServices.GetService<IErrorLoggingService>();
                    if (errorLoggingService != null)
                    {
                        await errorLoggingService.LogExceptionAsync(exception, errorContext);
                    }
                }
                catch (Exception loggingEx)
                {
                    _logger.LogCritical(loggingEx, "Failed to log exception via error logging service");
                }
            });

            // Create error response
            var errorResponse = CreateErrorResponse(exception, correlationId);
            var apiResponse = ApiResponse.ErrorResult(errorResponse);

            // Set response details
            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)GetStatusCode(exception);

            // Add correlation ID to response headers
            if (!context.Response.Headers.ContainsKey("X-Correlation-ID"))
            {
                context.Response.Headers.Add("X-Correlation-ID", correlationId);
            }

            // Serialize and write response
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = _environment.IsDevelopment()
            };

            var jsonResponse = JsonSerializer.Serialize(apiResponse, jsonOptions);
            await context.Response.WriteAsync(jsonResponse);

            // Log to standard logger as well
            LogException(exception, correlationId, context);
        }
        catch (Exception middlewareException)
        {
            // If middleware fails, log and return a basic error response
            _logger.LogCritical(middlewareException, "Exception occurred in GlobalExceptionMiddleware");
            
            await WriteBasicErrorResponse(context, correlationId);
        }
    }

    private ErrorContext CreateErrorContext(HttpContext context, string correlationId)
    {
        var request = context.Request;
        
        return new ErrorContext
        {
            CorrelationId = correlationId,
            RequestPath = request.Path.Value,
            HttpMethod = request.Method,
            UserAgent = request.Headers["User-Agent"].FirstOrDefault(),
            IpAddress = GetClientIpAddress(context),
            Environment = _environment.EnvironmentName,
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId.ToString(),
            ThreadId = Thread.CurrentThread.ManagedThreadId.ToString(),
            AdditionalData = new Dictionary<string, object>
            {
                ["QueryString"] = request.QueryString.Value ?? string.Empty,
                ["ContentType"] = request.ContentType ?? string.Empty,
                ["ContentLength"] = request.ContentLength ?? 0,
                ["Protocol"] = request.Protocol,
                ["Scheme"] = request.Scheme,
                ["Host"] = request.Host.Value,
                ["Headers"] = request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString())
            }
        };
    }

    private ErrorResponse CreateErrorResponse(Exception exception, string correlationId)
    {
        var errorResponse = new ErrorResponse
        {
            Code = GetErrorCode(exception),
            Message = GetUserFriendlyMessage(exception),
            CorrelationId = correlationId,
            Timestamp = DateTime.UtcNow
        };

        // Add details for custom exceptions
        if (exception is BaseException baseException)
        {
            errorResponse.Details = baseException.Details;
            errorResponse.Module = baseException.Module;
            
            if (baseException.Context.Any())
            {
                errorResponse.Context = baseException.Context;
            }
        }

        // Add stack trace and inner exception details in development
        if (_environment.IsDevelopment())
        {
            errorResponse.StackTrace = exception.StackTrace;
            
            if (exception.InnerException != null)
            {
                errorResponse.InnerError = new ErrorResponse
                {
                    Code = GetErrorCode(exception.InnerException),
                    Message = exception.InnerException.Message,
                    StackTrace = exception.InnerException.StackTrace
                };
            }
        }

        return errorResponse;
    }

    private static HttpStatusCode GetStatusCode(Exception exception)
    {
        return exception switch
        {
            BaseException baseEx => baseEx.StatusCode,
            ArgumentNullException => HttpStatusCode.BadRequest,
            ArgumentException => HttpStatusCode.BadRequest,
            UnauthorizedAccessException => HttpStatusCode.Unauthorized,
            NotImplementedException => HttpStatusCode.NotImplemented,
            TimeoutException => HttpStatusCode.RequestTimeout,
            _ => HttpStatusCode.InternalServerError
        };
    }

    private static string GetErrorCode(Exception exception)
    {
        return exception switch
        {
            BaseException baseEx => baseEx.ErrorCode,
            ArgumentNullException => "ARGUMENT_NULL",
            ArgumentException => "ARGUMENT_INVALID",
            UnauthorizedAccessException => "UNAUTHORIZED",
            NotImplementedException => "NOT_IMPLEMENTED",
            TimeoutException => "TIMEOUT",
            _ => "INTERNAL_SERVER_ERROR"
        };
    }

    private static string GetUserFriendlyMessage(Exception exception)
    {
        return exception switch
        {
            BaseException => exception.Message, // Custom exceptions have user-friendly messages
            ArgumentNullException => "A required parameter was not provided.",
            ArgumentException => "An invalid parameter was provided.",
            UnauthorizedAccessException => "You are not authorized to perform this action.",
            NotImplementedException => "This feature is not yet implemented.",
            TimeoutException => "The operation timed out. Please try again.",
            _ => "An unexpected error occurred. Please try again later."
        };
    }

    private static string GetOrCreateCorrelationId(HttpContext context)
    {
        // Try to get correlation ID from headers
        if (context.Request.Headers.TryGetValue("X-Correlation-ID", out var correlationId) && 
            !string.IsNullOrEmpty(correlationId))
        {
            return correlationId!;
        }

        // Try to get from trace identifier
        if (!string.IsNullOrEmpty(context.TraceIdentifier))
        {
            return context.TraceIdentifier;
        }

        // Generate new correlation ID
        return Guid.NewGuid().ToString();
    }

    private static string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (for load balancers/proxies)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    private void LogException(Exception exception, string correlationId, HttpContext context)
    {
        var logLevel = exception switch
        {
            BaseException baseEx when baseEx.StatusCode == HttpStatusCode.NotFound => LogLevel.Warning,
            BaseException baseEx when baseEx.StatusCode == HttpStatusCode.BadRequest => LogLevel.Warning,
            BaseException baseEx when baseEx.StatusCode == HttpStatusCode.Unauthorized => LogLevel.Warning,
            BaseException baseEx when baseEx.StatusCode == HttpStatusCode.Forbidden => LogLevel.Warning,
            _ => LogLevel.Error
        };

        _logger.Log(logLevel, exception,
            "Exception occurred. CorrelationId: {CorrelationId}, Path: {Path}, Method: {Method}, StatusCode: {StatusCode}",
            correlationId, context.Request.Path, context.Request.Method, (int)GetStatusCode(exception));
    }

    private static async Task WriteBasicErrorResponse(HttpContext context, string correlationId)
    {
        context.Response.ContentType = "application/json";
        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

        var basicResponse = new
        {
            success = false,
            message = "An unexpected error occurred. Please try again later.",
            error = new
            {
                code = "INTERNAL_SERVER_ERROR",
                message = "An unexpected error occurred. Please try again later.",
                correlationId = correlationId,
                timestamp = DateTime.UtcNow
            },
            timestamp = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(basicResponse, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(json);
    }
}
