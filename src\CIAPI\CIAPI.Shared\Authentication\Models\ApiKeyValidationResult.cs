namespace GDAPI.Shared.Authentication.Models;

/// <summary>
/// Result of API key validation
/// </summary>
public class ApiKeyValidationResult
{
    /// <summary>
    /// Whether the API key is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// API key information if valid
    /// </summary>
    public ApiKeyInfo? ApiKeyInfo { get; set; }

    /// <summary>
    /// Error message if validation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Whether rate limit was exceeded
    /// </summary>
    public bool RateLimitExceeded { get; set; }

    /// <summary>
    /// Remaining requests in current window
    /// </summary>
    public int RemainingRequests { get; set; }

    /// <summary>
    /// When the rate limit window resets
    /// </summary>
    public DateTime? RateLimitResetTime { get; set; }

    /// <summary>
    /// Create a successful validation result
    /// </summary>
    public static ApiKeyValidationResult Success(ApiKeyInfo apiKeyInfo, int remainingRequests = int.MaxValue)
    {
        return new ApiKeyValidationResult
        {
            IsValid = true,
            ApiKeyInfo = apiKeyInfo,
            RemainingRequests = remainingRequests
        };
    }

    /// <summary>
    /// Create a failed validation result
    /// </summary>
    public static ApiKeyValidationResult Failure(string errorMessage)
    {
        return new ApiKeyValidationResult
        {
            IsValid = false,
            ErrorMessage = errorMessage
        };
    }

    /// <summary>
    /// Create a rate limit exceeded result
    /// </summary>
    public static ApiKeyValidationResult RateLimitExceededResult(DateTime resetTime)
    {
        return new ApiKeyValidationResult
        {
            IsValid = false,
            RateLimitExceeded = true,
            ErrorMessage = "Rate limit exceeded",
            RateLimitResetTime = resetTime
        };
    }
}

/// <summary>
/// Information about an API key
/// </summary>
public class ApiKeyInfo
{
    /// <summary>
    /// Unique identifier for the API key
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Name or description of the API key
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Client or application name
    /// </summary>
    public string ClientName { get; set; } = string.Empty;

    /// <summary>
    /// Whether the API key is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Rate limit for this specific API key
    /// </summary>
    public int RateLimit { get; set; } = 1000;

    /// <summary>
    /// Rate limit window in minutes
    /// </summary>
    public int RateLimitWindowMinutes { get; set; } = 60;

    /// <summary>
    /// When the API key was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the API key expires (null = never expires)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Last time the API key was used
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// Additional metadata for the API key
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}
