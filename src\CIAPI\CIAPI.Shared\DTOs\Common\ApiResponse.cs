namespace CIAPI.Shared.DTOs.Common;

/// <summary>
/// Standard API response wrapper for consistent response format
/// </summary>
/// <typeparam name="T">Type of data being returned</typeparam>
public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public IEnumerable<string>? Errors { get; set; }
    public ErrorResponse? Error { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string? TraceId { get; set; }

    public static ApiResponse<T> SuccessResult(T data, string message = "Operation completed successfully")
    {
        return new ApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data
        };
    }

    public static ApiResponse<T> ErrorResult(string message, IEnumerable<string>? errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Errors = errors
        };
    }

    /// <summary>
    /// Create an error response with detailed error information
    /// </summary>
    public static ApiResponse<T> ErrorResult(ErrorResponse error)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = error.Message,
            Error = error,
            Timestamp = DateTime.UtcNow
        };
    }

    public static ApiResponse<T> ErrorResult(IEnumerable<string> errors)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = "One or more validation errors occurred",
            Errors = errors
        };
    }
}

/// <summary>
/// Non-generic API response for operations that don't return data
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    public static ApiResponse SuccessResult(string message = "Operation completed successfully")
    {
        return new ApiResponse
        {
            Success = true,
            Message = message
        };
    }

    public new static ApiResponse ErrorResult(string message, IEnumerable<string>? errors = null)
    {
        return new ApiResponse
        {
            Success = false,
            Message = message,
            Errors = errors
        };
    }

    public new static ApiResponse ErrorResult(IEnumerable<string> errors)
    {
        return new ApiResponse
        {
            Success = false,
            Message = "One or more validation errors occurred",
            Errors = errors
        };
    }

    /// <summary>
    /// Create an error response with detailed error information
    /// </summary>
    public new static ApiResponse ErrorResult(ErrorResponse error)
    {
        return new ApiResponse
        {
            Success = false,
            Message = error.Message,
            Error = error,
            Timestamp = DateTime.UtcNow
        };
    }
}
