# AuthoringTool Module - Testing Guide

## 🚀 Quick Start

### 1. Database Setup
First, run the database setup script to create the Authoring table and sample data:

```sql
-- Execute this script in SQL Server Management Studio or Azure Data Studio
-- File: docs/authoringtool-complete-setup.sql
```

### 2. Start the Application
The application is already running on `http://localhost:5199`

### 3. Access Swagger UI
Open your browser and navigate to: `http://localhost:5199`

## 📋 API Testing Scenarios

### Scenario 1: Basic Authoring Retrieval
**Test the basic endpoint functionality**

```bash
# Get all authoring items (first page)
curl -X GET "http://localhost:5199/api/authoring"

# Get authoring items with pagination
curl -X GET "http://localhost:5199/api/authoring?pageNumber=1&pageSize=5"
```

**Expected Result:**
- Status: 200 OK
- Returns paginated list of authoring items
- Should show 10 total authoring items with various statuses

### Scenario 2: Search Functionality
**Test authoring search capabilities**

```bash
# Search by title/description
curl -X GET "http://localhost:5199/api/authoring?searchTerm=protocol"

# Search for clinical authoring items
curl -X GET "http://localhost:5199/api/authoring?searchTerm=clinical"

# Search for safety reports
curl -X GET "http://localhost:5199/api/authoring?searchTerm=safety"
```

**Expected Result:**
- Returns filtered authoring items matching search terms
- Search works on both title and description fields

### Scenario 3: Status Filtering
**Test filtering by authoring status**

```bash
# Get draft authoring items
curl -X GET "http://localhost:5199/api/authoring?statusFilter=Draft"

# Get authoring items in review
curl -X GET "http://localhost:5199/api/authoring?statusFilter=InReview"

# Get approved authoring items
curl -X GET "http://localhost:5199/api/authoring?statusFilter=Approved"

# Get published authoring items
curl -X GET "http://localhost:5199/api/authoring?statusFilter=Published"

# Get archived authoring items
curl -X GET "http://localhost:5199/api/authoring?statusFilter=Archived"
```

**Expected Result:**
- Each request returns only authoring items with the specified status
- Draft: 3 authoring items
- InReview: 2 authoring items
- Approved: 2 authoring items
- Published: 2 authoring items
- Archived: 1 authoring item

### Scenario 4: Department Filtering
**Test filtering by department**

```bash
# Clinical Research authoring items
curl -X GET "http://localhost:5199/api/authoring?departmentFilter=Clinical%20Research"

# Regulatory Affairs authoring items
curl -X GET "http://localhost:5199/api/authoring?departmentFilter=Regulatory%20Affairs"

# Quality Assurance authoring items
curl -X GET "http://localhost:5199/api/authoring?departmentFilter=Quality%20Assurance"
```

**Expected Result:**
- Returns authoring items filtered by department
- Each department should have specific authoring items

### Scenario 5: Category Filtering
**Test filtering by authoring category**

```bash
# Clinical category authoring items
curl -X GET "http://localhost:5199/api/authoring?categoryFilter=Clinical"

# Regulatory category authoring items
curl -X GET "http://localhost:5199/api/authoring?categoryFilter=Regulatory"

# Manufacturing category authoring items
curl -X GET "http://localhost:5199/api/authoring?categoryFilter=Manufacturing"
```

### Scenario 6: Author Filtering
**Test filtering by author**

```bash
# Authoring items by Dr. Jane Smith
curl -X GET "http://localhost:5199/api/authoring?authorFilter=Dr.%20Jane%20Smith"

# Authoring items by Dr. Robert Chen
curl -X GET "http://localhost:5199/api/authoring?authorFilter=Dr.%20Robert%20Chen"
```

### Scenario 7: Combined Filters
**Test multiple filters together**

```bash
# Draft authoring items in Clinical Research
curl -X GET "http://localhost:5199/api/authoring?statusFilter=Draft&departmentFilter=Clinical%20Research"

# High priority authoring items
curl -X GET "http://localhost:5199/api/authoring?searchTerm=High"

# Clinical authoring items by specific author
curl -X GET "http://localhost:5199/api/authoring?categoryFilter=Clinical&authorFilter=Dr.%20Jane%20Smith"
```

### Scenario 8: Pagination Testing
**Test pagination with different page sizes**

```bash
# Small page size
curl -X GET "http://localhost:5199/api/authoring?pageSize=3"

# Large page size
curl -X GET "http://localhost:5199/api/authoring?pageSize=20"

# Second page
curl -X GET "http://localhost:5199/api/authoring?pageNumber=2&pageSize=5"
```

### Scenario 9: Error Handling
**Test error scenarios**

```bash
# Invalid page number
curl -X GET "http://localhost:5199/api/authoring?pageNumber=0"

# Invalid page size
curl -X GET "http://localhost:5199/api/authoring?pageSize=101"

# Invalid page size (negative)
curl -X GET "http://localhost:5199/api/authoring?pageSize=-1"
```

**Expected Result:**
- Status: 400 Bad Request
- Appropriate error messages

### Scenario 10: Health Check
**Test the health endpoint**

```bash
# Health check
curl -X GET "http://localhost:5199/api/authoring/health"
```

**Expected Result:**
```json
{
  "status": "Healthy",
  "module": "Authoring",
  "timestamp": "2024-01-20T15:45:30Z"
}
```

## 🔍 Response Validation

### Sample Response Structure
```json
{
  "success": true,
  "message": "Documents retrieved successfully",
  "data": {
    "items": [
      {
        "id": 1,
        "title": "Clinical Trial Protocol - Phase II Study",
        "description": "Protocol for the Phase II clinical trial...",
        "documentType": "Protocol",
        "category": "Clinical",
        "tags": "Phase II, Drug XYZ, ABC Disease",
        "author": "Dr. Jane Smith",
        "coAuthors": "Dr. John Doe, Dr. Emily Johnson",
        "status": "Draft",
        "version": "0.1",
        "fileName": "Protocol_XYZ_Phase2_Draft.docx",
        "fileSize": 2048000,
        "fileFormat": "DOCX",
        "department": "Clinical Research",
        "project": "XYZ Development",
        "priority": "High",
        "statusDisplay": "📝 Draft",
        "priorityDisplay": "🔴 High",
        "fileSizeDisplay": "2.0 MB",
        "isOverdue": false,
        "authorInfo": "Dr. Jane Smith (with Dr. John Doe, Dr. Emily Johnson)",
        "isActive": true,
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 10,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false,
    "nextPageNumber": null,
    "previousPageNumber": null
  },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

### Key Validation Points
1. **Status Display**: Should show emoji icons (📝 Draft, 👀 In Review, ✅ Approved, 🚀 Published, 📦 Archived)
2. **Priority Display**: Should show colored indicators (🔴 High, 🟡 Medium, 🟢 Low)
3. **File Size Display**: Should show human-readable format (2.0 MB, 1.0 KB, etc.)
4. **Overdue Detection**: Should correctly identify overdue documents
5. **Author Info**: Should combine author and co-authors appropriately
6. **Pagination**: Should correctly calculate total pages and navigation flags

## 🧪 Advanced Testing

### Performance Testing
```bash
# Test with maximum page size
curl -X GET "http://localhost:5199/api/documents?pageSize=100"

# Test multiple concurrent requests
for i in {1..10}; do curl -X GET "http://localhost:5199/api/documents" & done
```

### Data Validation
1. Verify all 10 sample documents are returned
2. Check that computed properties are correctly calculated
3. Ensure filtering works correctly with special characters
4. Validate that pagination math is correct

### Browser Testing
1. Open `http://localhost:5199` in browser
2. Navigate to Documents endpoints in Swagger UI
3. Test different parameter combinations
4. Verify response formatting

## 🐛 Troubleshooting

### Common Issues
1. **Database Connection**: Ensure SQL Server is running and connection string is correct
2. **Missing Data**: Run the setup script to create sample data
3. **Port Conflicts**: Application runs on port 5199 by default
4. **CORS Issues**: CORS is configured to allow all origins in development

### Logs to Check
- Application startup logs
- Database connection logs
- API request/response logs
- Error logs for failed requests

## ✅ Success Criteria

The AuthoringTool module is working correctly if:
1. All API endpoints return 200 OK status
2. Sample data is properly loaded (10 documents)
3. Filtering and search work as expected
4. Pagination calculations are correct
5. Error handling works for invalid inputs
6. Health check endpoint responds correctly
7. Swagger documentation is accessible and functional
