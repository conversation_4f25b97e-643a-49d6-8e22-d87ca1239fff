using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using CIAPI.Shared.Authentication.Models;
using CIAPI.Shared.Authentication.Services;
using CIAPI.Shared.DTOs.Common;

namespace CIAPI.Shared.Authentication.Middleware;

/// <summary>
/// Middleware for API key authentication
/// </summary>
public class ApiKeyAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ApiKeyAuthenticationMiddleware> _logger;
    private readonly ApiKeySettings _settings;
    private readonly IApiKeyService _apiKeyService;

    // Endpoints that don't require authentication
    private readonly HashSet<string> _publicEndpoints = new(StringComparer.OrdinalIgnoreCase)
    {
        "/health",
        "/swagger",
        "/swagger/index.html",
        "/swagger/v1/swagger.json",
        "/api/auth/generate-key" // For initial setup
    };

    public ApiKeyAuthenticationMiddleware(
        RequestDelegate next,
        ILogger<ApiKeyAuthenticationMiddleware> logger,
        IOptions<ApiKeySettings> settings,
        IApiKeyService apiKeyService)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
        _apiKeyService = apiKeyService ?? throw new ArgumentNullException(nameof(apiKeyService));
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Skip authentication for public endpoints
        if (IsPublicEndpoint(context.Request.Path))
        {
            await _next(context);
            return;
        }

        // Skip authentication for OPTIONS requests (CORS preflight)
        if (context.Request.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            await _next(context);
            return;
        }

        try
        {
            // Extract API key from request
            var apiKey = ExtractApiKey(context.Request);

            if (string.IsNullOrEmpty(apiKey))
            {
                await WriteUnauthorizedResponse(context, "API key is required");
                return;
            }

            // Validate API key
            var clientIpAddress = GetClientIpAddress(context);
            var validationResult = await _apiKeyService.ValidateApiKeyAsync(apiKey, clientIpAddress);

            if (!validationResult.IsValid)
            {
                if (validationResult.RateLimitExceeded)
                {
                    await WriteRateLimitResponse(context, validationResult);
                    return;
                }

                await WriteUnauthorizedResponse(context, validationResult.ErrorMessage ?? "Invalid API key");
                return;
            }

            // Add API key information to context for use in controllers
            context.Items["ApiKeyInfo"] = validationResult.ApiKeyInfo;
            context.Items["RemainingRequests"] = validationResult.RemainingRequests;

            // Add rate limit headers
            AddRateLimitHeaders(context.Response, validationResult);

            // Continue to next middleware
            await _next(context);

            // Update last used timestamp after successful request
            _ = Task.Run(async () =>
            {
                try
                {
                    await _apiKeyService.UpdateLastUsedAsync(apiKey);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to update API key last used timestamp");
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in API key authentication middleware");
            await WriteInternalServerErrorResponse(context, "Authentication error occurred");
        }
    }

    private bool IsPublicEndpoint(PathString path)
    {
        var pathValue = path.Value ?? string.Empty;
        
        return _publicEndpoints.Any(endpoint => 
            pathValue.StartsWith(endpoint, StringComparison.OrdinalIgnoreCase));
    }

    private string? ExtractApiKey(HttpRequest request)
    {
        // Try to get API key from header first
        if (request.Headers.TryGetValue(_settings.HeaderName, out var headerValue))
        {
            return headerValue.FirstOrDefault();
        }

        // Try to get API key from query parameter if allowed
        if (_settings.AllowQueryParameter && 
            request.Query.TryGetValue(_settings.QueryParameterName, out var queryValue))
        {
            return queryValue.FirstOrDefault();
        }

        return null;
    }

    private static string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (for load balancers/proxies)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    private static void AddRateLimitHeaders(HttpResponse response, ApiKeyValidationResult validationResult)
    {
        if (validationResult.ApiKeyInfo != null)
        {
            response.Headers.Add("X-RateLimit-Limit", validationResult.ApiKeyInfo.RateLimit.ToString());
            response.Headers.Add("X-RateLimit-Remaining", validationResult.RemainingRequests.ToString());
            
            if (validationResult.RateLimitResetTime.HasValue)
            {
                var resetTimeUnix = ((DateTimeOffset)validationResult.RateLimitResetTime.Value).ToUnixTimeSeconds();
                response.Headers.Add("X-RateLimit-Reset", resetTimeUnix.ToString());
            }
        }
    }

    private static async Task WriteUnauthorizedResponse(HttpContext context, string message)
    {
        context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
        context.Response.ContentType = "application/json";

        var response = new
        {
            success = false,
            message = "Authentication required",
            error = new
            {
                code = "UNAUTHORIZED",
                message = message,
                timestamp = DateTime.UtcNow
            },
            timestamp = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(json);
    }

    private static async Task WriteRateLimitResponse(HttpContext context, ApiKeyValidationResult validationResult)
    {
        context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
        context.Response.ContentType = "application/json";

        // Add rate limit headers
        AddRateLimitHeaders(context.Response, validationResult);

        var response = new
        {
            success = false,
            message = "Rate limit exceeded",
            error = new
            {
                code = "RATE_LIMIT_EXCEEDED",
                message = "Too many requests. Please try again later.",
                retryAfter = validationResult.RateLimitResetTime,
                timestamp = DateTime.UtcNow
            },
            timestamp = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(json);
    }

    private static async Task WriteInternalServerErrorResponse(HttpContext context, string message)
    {
        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
        context.Response.ContentType = "application/json";

        var response = new
        {
            success = false,
            message = "Internal server error",
            error = new
            {
                code = "INTERNAL_SERVER_ERROR",
                message = message,
                timestamp = DateTime.UtcNow
            },
            timestamp = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(json);
    }
}
