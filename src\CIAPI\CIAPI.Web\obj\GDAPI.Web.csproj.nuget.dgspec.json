{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "projectName": "GDAPI.Web", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\GDAPI.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\AuthoringTool\\GDAPI.Modules.AuthoringTool.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\ProductManagement\\GDAPI.Modules.ProductManagement.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\ProductManagement\\GDAPI.Modules.ProductManagement.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Modules\\UserManagement\\GDAPI.Modules.UserManagement.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Shared\\GDAPI.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\GDAPI.Shared\\GDAPI.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.6, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}