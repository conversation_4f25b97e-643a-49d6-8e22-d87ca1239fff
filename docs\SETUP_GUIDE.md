# CI Solution - Setup Guide

## Overview

CI Solution is a modern, enterprise-grade application built with a **Modular Monolithic Architecture**. It consists of three main components:

1. **CIAPI** - Centralized .NET Core Web API
2. **CIDashboard** - ASP.NET MVC Core Dashboard
3. **CIWeb** - Next.js Frontend Application

## Prerequisites

Before setting up the CI Solution, ensure you have the following installed:

- **.NET 8.0 SDK** or later
- **Node.js 18+** and npm
- **SQL Server** (LocalDB or full instance)
- **Visual Studio 2022** or **VS Code** (recommended)

## Quick Start

### 1. Clone and Navigate to Project

```bash
cd CISolution
```

### 2. Database Setup

1. **Create Database:**
   ```bash
   # Run the database schema script
   sqlcmd -S "(localdb)\mssqllocaldb" -i docs/database-schema.sql
   ```

2. **Verify Connection String:**
   - Check `src/CIAPI/CIAPI.Web/appsettings.json`
   - Default: `Server=(localdb)\\mssqllocaldb;Database=CISolutionDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true`

### 3. Start CIAPI (Required First)

```bash
# Build and run the API
dotnet build
dotnet run --project src/CIAPI/CIAPI.Web/CIAPI.Web.csproj
```

The API will be available at: **http://localhost:5199**

- Swagger UI: http://localhost:5199
- Health Check: http://localhost:5199/health
- Users API: http://localhost:5199/api/users

### 4. Start CIDashboard

```bash
# In a new terminal
dotnet run --project src/CIDashboard/CIDashboard.csproj
```

The Dashboard will be available at: **http://localhost:5228**

### 5. Start CIWeb (Next.js Frontend)

```bash
# In a new terminal, navigate to the frontend
cd src/ciweb

# Install dependencies (first time only)
npm install

# Start development server
npm run dev
```

The Frontend will be available at: **http://localhost:3000**

## Application URLs

| Component | URL | Description |
|-----------|-----|-------------|
| **CIAPI** | http://localhost:5199 | Centralized Web API with Swagger UI |
| **CIDashboard** | http://localhost:5228 | Admin Dashboard (ASP.NET MVC) |
| **CIWeb** | http://localhost:3000 | User Frontend (Next.js) |

## Default Credentials

The system creates a default admin user:

- **Email:** <EMAIL>
- **Password:** Admin@123

## Project Structure

```
CISolution/
├── src/
│   ├── CIAPI/                    # Centralized Web API
│   │   ├── CIAPI.Web/           # Main API project
│   │   ├── CIAPI.Core/          # Domain models & interfaces
│   │   ├── CIAPI.Infrastructure/ # Data access layer
│   │   ├── CIAPI.Modules/       # Business modules
│   │   │   ├── UserManagement/  # User management module
│   │   │   └── ProductManagement/ # Product module (placeholder)
│   │   └── CIAPI.Shared/        # Shared utilities
│   ├── CIDashboard/             # ASP.NET MVC Dashboard
│   └── ciweb/                   # Next.js Frontend
├── tests/                       # Test projects
├── docs/                        # Documentation
│   ├── database-schema.sql      # Database setup script
│   └── SETUP_GUIDE.md          # This file
└── CISolution.sln              # Solution file
```

## Key Features

### CIAPI Features
- ✅ Modular Monolithic Architecture
- ✅ Clean Architecture with Repository Pattern
- ✅ ADO.NET with Dapper for data access
- ✅ Swagger/OpenAPI documentation
- ✅ CORS configuration
- ✅ Comprehensive error handling
- ✅ Health check endpoints

### CIDashboard Features
- ✅ ASP.NET MVC Core with Bootstrap UI
- ✅ API integration with HttpClient
- ✅ User management interface
- ✅ Responsive design
- ✅ Real-time API health monitoring

### CIWeb Features
- ✅ Next.js 15 with TypeScript
- ✅ Tailwind CSS for styling
- ✅ API integration with fetch
- ✅ Responsive user interface
- ✅ Real-time API status monitoring

## Development Workflow

1. **Start CIAPI first** - All other components depend on it
2. **Verify API health** - Check http://localhost:5199/health
3. **Start Dashboard and/or Frontend** as needed
4. **Use Swagger UI** for API testing and documentation

## Troubleshooting

### Common Issues

1. **API not starting:**
   - Check if port 5199 is available
   - Verify database connection string
   - Ensure SQL Server is running

2. **Database connection issues:**
   - Verify SQL Server LocalDB is installed
   - Check connection string in appsettings.json
   - Run database schema script

3. **Frontend API connection issues:**
   - Ensure CIAPI is running first
   - Check CORS configuration
   - Verify API URL in .env.local

### Port Configuration

If you need to change default ports:

- **CIAPI:** Modify `src/CIAPI/CIAPI.Web/Properties/launchSettings.json`
- **CIDashboard:** Modify `src/CIDashboard/Properties/launchSettings.json`
- **CIWeb:** Modify `src/ciweb/package.json` dev script or use `npm run dev -- -p 3001`

## Next Steps

1. **Implement Authentication:** Add JWT authentication to secure the API
2. **Add More Modules:** Extend the modular architecture with additional business modules
3. **Testing:** Implement unit and integration tests
4. **Deployment:** Configure for production deployment

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the API documentation at http://localhost:5199
3. Check application logs in the console output

---

**Built with ❤️ using modern .NET and React technologies**
