<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CIAPI.Shared\CIAPI.Shared.csproj" />
    <ProjectReference Include="..\CIAPI.Modules\UserManagement\CIAPI.Modules.UserManagement.csproj" />
    <ProjectReference Include="..\CIAPI.Modules\ProductManagement\CIAPI.Modules.ProductManagement.csproj" />
    <ProjectReference Include="..\CIAPI.Modules\AuthoringTool\CIAPI.Modules.AuthoringTool.csproj" />
  </ItemGroup>

</Project>
