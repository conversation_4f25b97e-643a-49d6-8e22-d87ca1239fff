using Microsoft.AspNetCore.Mvc;
using CIDashboard.Services;
using CIDashboard.Models;

namespace CIDashboard.Controllers;

/// <summary>
/// Users management controller for dashboard
/// </summary>
public class UsersController : Controller
{
    private readonly ApiService _apiService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(ApiService apiService, ILogger<UsersController> logger)
    {
        _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Display users list
    /// </summary>
    public async Task<IActionResult> Index(int pageNumber = 1, int pageSize = 10, string? searchTerm = null, string? sortBy = null, bool sortDescending = false)
    {
        try
        {
            _logger.LogInformation("Loading users list. Page: {PageNumber}, Size: {PageSize}, Search: {SearchTerm}", 
                pageNumber, pageSize, searchTerm);

            var queryParams = new List<string>();
            queryParams.Add($"pageNumber={pageNumber}");
            queryParams.Add($"pageSize={pageSize}");
            
            if (!string.IsNullOrEmpty(searchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(searchTerm)}");
            
            if (!string.IsNullOrEmpty(sortBy))
                queryParams.Add($"sortBy={Uri.EscapeDataString(sortBy)}");
            
            queryParams.Add($"sortDescending={sortDescending}");

            var endpoint = $"/api/users?{string.Join("&", queryParams)}";
            var response = await _apiService.GetAsync<ApiResponse<PagedResult<UserDto>>>(endpoint);

            var viewModel = new UserListViewModel
            {
                Users = response?.Data ?? new PagedResult<UserDto>(),
                SearchTerm = searchTerm,
                PageNumber = pageNumber,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDescending = sortDescending
            };

            if (response?.Success != true)
            {
                TempData["ErrorMessage"] = response?.Message ?? "Failed to load users";
            }

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while loading users list");
            TempData["ErrorMessage"] = "An error occurred while loading users";
            
            return View(new UserListViewModel());
        }
    }

    /// <summary>
    /// Display user details
    /// </summary>
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            _logger.LogInformation("Loading user details for ID: {UserId}", id);

            var response = await _apiService.GetAsync<ApiResponse<UserDto>>($"/api/users/{id}");

            if (response?.Success == true && response.Data != null)
            {
                var viewModel = new UserDetailsViewModel
                {
                    User = response.Data,
                    CanEdit = true, // TODO: Implement proper authorization
                    CanDelete = true, // TODO: Implement proper authorization
                    AvailableRoles = new[] { "User", "Admin", "Manager", "SuperAdmin" }
                };

                return View(viewModel);
            }

            TempData["ErrorMessage"] = response?.Message ?? "User not found";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while loading user details for ID: {UserId}", id);
            TempData["ErrorMessage"] = "An error occurred while loading user details";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Display create user form
    /// </summary>
    public IActionResult Create()
    {
        var viewModel = new UserFormViewModel
        {
            AvailableRoles = new[] { "User", "Admin", "Manager" }
        };

        return View(viewModel);
    }

    /// <summary>
    /// Handle create user form submission
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(UserFormViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                model.AvailableRoles = new[] { "User", "Admin", "Manager" };
                return View(model);
            }

            _logger.LogInformation("Creating new user with email: {Email}", model.Email);

            var request = new CreateUserRequest
            {
                FirstName = model.FirstName,
                LastName = model.LastName,
                Email = model.Email,
                PhoneNumber = model.PhoneNumber,
                Password = model.Password ?? string.Empty,
                ConfirmPassword = model.ConfirmPassword ?? string.Empty,
                ProfileImageUrl = model.ProfileImageUrl,
                EmailConfirmed = model.EmailConfirmed,
                PhoneConfirmed = model.PhoneConfirmed,
                IsActive = model.IsActive,
                Roles = model.SelectedRoles
            };

            var response = await _apiService.PostAsync<CreateUserRequest, ApiResponse<UserDto>>("/api/users", request);

            if (response?.Success == true)
            {
                TempData["SuccessMessage"] = "User created successfully";
                return RedirectToAction(nameof(Index));
            }

            if (response?.Errors != null && response.Errors.Any())
            {
                foreach (var error in response.Errors)
                {
                    ModelState.AddModelError(string.Empty, error);
                }
            }
            else
            {
                ModelState.AddModelError(string.Empty, response?.Message ?? "Failed to create user");
            }

            model.AvailableRoles = new[] { "User", "Admin", "Manager" };
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating user");
            ModelState.AddModelError(string.Empty, "An error occurred while creating the user");
            
            model.AvailableRoles = new[] { "User", "Admin", "Manager" };
            return View(model);
        }
    }

    /// <summary>
    /// Display edit user form
    /// </summary>
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            _logger.LogInformation("Loading user for editing. ID: {UserId}", id);

            var response = await _apiService.GetAsync<ApiResponse<UserDto>>($"/api/users/{id}");

            if (response?.Success == true && response.Data != null)
            {
                var user = response.Data;
                var viewModel = new UserFormViewModel
                {
                    Id = user.Id,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    ProfileImageUrl = user.ProfileImageUrl,
                    EmailConfirmed = user.EmailConfirmed,
                    PhoneConfirmed = user.PhoneConfirmed,
                    TwoFactorEnabled = user.TwoFactorEnabled,
                    IsActive = user.IsActive,
                    SelectedRoles = user.Roles,
                    AvailableRoles = new[] { "User", "Admin", "Manager", "SuperAdmin" }
                };

                return View(viewModel);
            }

            TempData["ErrorMessage"] = response?.Message ?? "User not found";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while loading user for editing. ID: {UserId}", id);
            TempData["ErrorMessage"] = "An error occurred while loading user details";
            return RedirectToAction(nameof(Index));
        }
    }

    /// <summary>
    /// Handle edit user form submission
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, UserFormViewModel model)
    {
        try
        {
            if (id != model.Id)
            {
                return BadRequest();
            }

            if (!ModelState.IsValid)
            {
                model.AvailableRoles = new[] { "User", "Admin", "Manager", "SuperAdmin" };
                return View(model);
            }

            _logger.LogInformation("Updating user with ID: {UserId}", id);

            var request = new UpdateUserRequest
            {
                FirstName = model.FirstName,
                LastName = model.LastName,
                Email = model.Email,
                PhoneNumber = model.PhoneNumber,
                ProfileImageUrl = model.ProfileImageUrl,
                EmailConfirmed = model.EmailConfirmed,
                PhoneConfirmed = model.PhoneConfirmed,
                TwoFactorEnabled = model.TwoFactorEnabled,
                IsActive = model.IsActive,
                Roles = model.SelectedRoles
            };

            var response = await _apiService.PutAsync<UpdateUserRequest, ApiResponse<UserDto>>($"/api/users/{id}", request);

            if (response?.Success == true)
            {
                TempData["SuccessMessage"] = "User updated successfully";
                return RedirectToAction(nameof(Details), new { id });
            }

            if (response?.Errors != null && response.Errors.Any())
            {
                foreach (var error in response.Errors)
                {
                    ModelState.AddModelError(string.Empty, error);
                }
            }
            else
            {
                ModelState.AddModelError(string.Empty, response?.Message ?? "Failed to update user");
            }

            model.AvailableRoles = new[] { "User", "Admin", "Manager", "SuperAdmin" };
            return View(model);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating user with ID: {UserId}", id);
            ModelState.AddModelError(string.Empty, "An error occurred while updating the user");
            
            model.AvailableRoles = new[] { "User", "Admin", "Manager", "SuperAdmin" };
            return View(model);
        }
    }

    /// <summary>
    /// Handle delete user request
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            _logger.LogInformation("Deleting user with ID: {UserId}", id);

            var success = await _apiService.DeleteAsync($"/api/users/{id}");

            if (success)
            {
                TempData["SuccessMessage"] = "User deleted successfully";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to delete user";
            }

            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting user with ID: {UserId}", id);
            TempData["ErrorMessage"] = "An error occurred while deleting the user";
            return RedirectToAction(nameof(Index));
        }
    }
}
