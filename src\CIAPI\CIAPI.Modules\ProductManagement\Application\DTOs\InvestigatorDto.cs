using System.ComponentModel.DataAnnotations;

namespace GDAPI.Modules.ProductManagement.Application.DTOs;

/// <summary>
/// Investigator data transfer object for API responses
/// </summary>
public class InvestigatorDto
{
    public int Id { get; set; }
    public string InvestigatorName { get; set; } = string.Empty;
    public string? SpecializationNames { get; set; }
    public string? Designation { get; set; }
    public string? Organisation { get; set; }
    public string? ContactNumber { get; set; }
    public string? EmailID { get; set; }
    public string? Fax { get; set; }
    public string? RegionName { get; set; }
    public string? CountryName { get; set; }
    public string? StateName { get; set; }
    public string? CityName { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Computed properties
    public string FullLocation { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
}

/// <summary>
/// Create investigator request DTO
/// </summary>
public class CreateInvestigatorRequest
{
    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string InvestigatorName { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string? SpecializationNames { get; set; }
    
    [StringLength(100)]
    public string? Designation { get; set; }
    
    [StringLength(200)]
    public string? Organisation { get; set; }
    
    [Phone]
    [StringLength(20)]
    public string? ContactNumber { get; set; }
    
    [EmailAddress]
    [StringLength(255)]
    public string? EmailID { get; set; }
    
    [StringLength(20)]
    public string? Fax { get; set; }
    
    [StringLength(100)]
    public string? RegionName { get; set; }
    
    [StringLength(100)]
    public string? CountryName { get; set; }
    
    [StringLength(100)]
    public string? StateName { get; set; }
    
    [StringLength(100)]
    public string? CityName { get; set; }
    
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Update investigator request DTO
/// </summary>
public class UpdateInvestigatorRequest
{
    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string InvestigatorName { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string? SpecializationNames { get; set; }
    
    [StringLength(100)]
    public string? Designation { get; set; }
    
    [StringLength(200)]
    public string? Organisation { get; set; }
    
    [Phone]
    [StringLength(20)]
    public string? ContactNumber { get; set; }
    
    [EmailAddress]
    [StringLength(255)]
    public string? EmailID { get; set; }
    
    [StringLength(20)]
    public string? Fax { get; set; }
    
    [StringLength(100)]
    public string? RegionName { get; set; }
    
    [StringLength(100)]
    public string? CountryName { get; set; }
    
    [StringLength(100)]
    public string? StateName { get; set; }
    
    [StringLength(100)]
    public string? CityName { get; set; }
    
    public bool IsActive { get; set; }
}

/// <summary>
/// Investigator search request DTO
/// </summary>
public class InvestigatorSearchRequest
{
    public string? SearchTerm { get; set; }
    public string? RegionFilter { get; set; }
    public string? CountryFilter { get; set; }
    public string? SpecializationFilter { get; set; }
    public string? OrganisationFilter { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Investigator filters DTO for dropdown lists
/// </summary>
public class InvestigatorFiltersDto
{
    public IEnumerable<string> Regions { get; set; } = new List<string>();
    public IEnumerable<string> Countries { get; set; } = new List<string>();
    public IEnumerable<string> Specializations { get; set; } = new List<string>();
}

/// <summary>
/// Investigator statistics DTO
/// </summary>
public class InvestigatorStatsDto
{
    public int TotalInvestigators { get; set; }
    public int ActiveInvestigators { get; set; }
    public int InactiveInvestigators { get; set; }
    public int TotalRegions { get; set; }
    public int TotalCountries { get; set; }
    public int TotalOrganisations { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
