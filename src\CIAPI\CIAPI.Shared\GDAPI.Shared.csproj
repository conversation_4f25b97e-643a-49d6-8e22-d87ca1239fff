﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="System.Text.Json" Version="9.0.7" />
  </ItemGroup>

</Project>
