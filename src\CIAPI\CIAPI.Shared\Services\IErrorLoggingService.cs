using GDAPI.Shared.DTOs.Common;
using GDAPI.Shared.Exceptions;

namespace GDAPI.Shared.Services;

/// <summary>
/// Service interface for logging errors to multiple destinations
/// </summary>
public interface IErrorLoggingService
{
    /// <summary>
    /// Log an exception with context information
    /// </summary>
    /// <param name="exception">The exception to log</param>
    /// <param name="context">Additional context information</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task LogExceptionAsync(Exception exception, ErrorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// Log an error with custom details
    /// </summary>
    /// <param name="errorCode">Error code</param>
    /// <param name="message">Error message</param>
    /// <param name="context">Additional context information</param>
    /// <param name="severity">Error severity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task LogErrorAsync(string errorCode, string message, ErrorContext context, string severity = "Error", CancellationToken cancellationToken = default);

    /// <summary>
    /// Get error logs with filtering and pagination
    /// </summary>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="module">Module filter</param>
    /// <param name="severity">Severity filter</param>
    /// <param name="fromDate">From date filter</param>
    /// <param name="toDate">To date filter</param>
    /// <param name="correlationId">Correlation ID filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated error logs</returns>
    Task<(IEnumerable<ErrorLogEntry> Items, int TotalCount)> GetErrorLogsAsync(
        int pageNumber = 1,
        int pageSize = 50,
        string? module = null,
        string? severity = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? correlationId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clean up old error logs
    /// </summary>
    /// <param name="retentionDays">Number of days to retain logs</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of deleted records</returns>
    Task<int> CleanupOldLogsAsync(int retentionDays = 90, CancellationToken cancellationToken = default);
}

/// <summary>
/// Context information for error logging
/// </summary>
public class ErrorContext
{
    public string? CorrelationId { get; set; }
    public string? Module { get; set; }
    public string? UserId { get; set; }
    public string? RequestPath { get; set; }
    public string? HttpMethod { get; set; }
    public string? UserAgent { get; set; }
    public string? IpAddress { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
    public string? Environment { get; set; }
    public string? MachineName { get; set; }
    public string? ProcessId { get; set; }
    public string? ThreadId { get; set; }
}
