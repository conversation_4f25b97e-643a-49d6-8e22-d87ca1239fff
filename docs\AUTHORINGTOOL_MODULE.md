# AuthoringTool Module - Implementation Guide

## Overview

The AuthoringTool module has been successfully implemented in the **CIAPI.Modules.AuthoringTool** namespace, featuring the **GetAuthoring_Sample** stored procedure integration with full async implementation and centralized API connectivity.

## 🎯 Key Features Implemented

### ✅ Database Layer
- **Authoring Table** with comprehensive authoring management columns
- **GetAuthoring_Sample Stored Procedure**
- **Sample Data** with 10 test authoring items covering various statuses and types
- **Proper Indexing** for performance optimization

### ✅ Repository Layer (ADO.NET + Dapper)
- **IAuthoringRepository** interface with comprehensive methods
- **AuthoringRepository** implementation with async operations
- **Stored Procedure Integration** using Dapper
- **Advanced Filtering** and search capabilities

### ✅ Service Layer
- **IAuthoringService** interface with business logic
- **AuthoringService** implementation with error handling
- **DTO Mapping** between entities and API responses
- **Comprehensive Logging** for debugging and monitoring

### ✅ API Layer
- **AuthoringController** with RESTful endpoints
- **Swagger Documentation** for all endpoints
- **Proper Error Handling** with consistent responses
- **Input Validation** using data annotations

## 📊 Database Schema

### Authoring Table Structure
```sql
CREATE TABLE Authoring (
    Id int IDENTITY(1,1) PRIMARY KEY,
    Title nvarchar(200) NOT NULL,
    Description nvarchar(1000) NULL,
    AuthoringType nvarchar(50) NOT NULL,
    Category nvarchar(100) NULL,
    Tags nvarchar(500) NULL,
    Author nvarchar(100) NOT NULL,
    CoAuthors nvarchar(100) NULL,
    Status nvarchar(50) NOT NULL DEFAULT 'Draft',
    Version nvarchar(20) NULL,
    FilePath nvarchar(500) NULL,
    FileName nvarchar(100) NULL,
    FileSize bigint NULL,
    FileFormat nvarchar(50) NULL,
    LastModified datetime2 NULL,
    PublishedDate datetime2 NULL,
    Department nvarchar(100) NULL,
    Project nvarchar(100) NULL,
    Priority nvarchar(50) NULL,
    DueDate datetime2 NULL,
    ReviewComments nvarchar(1000) NULL,
    ReviewedBy nvarchar(100) NULL,
    ReviewedDate datetime2 NULL,
    ApprovedBy nvarchar(100) NULL,
    ApprovedDate datetime2 NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt datetime2 NULL,
    CreatedBy nvarchar(100) NULL,
    UpdatedBy nvarchar(100) NULL,
    IsActive bit NOT NULL DEFAULT 1,
    IsDeleted bit NOT NULL DEFAULT 0,
    RowVersion rowversion
);
```

### Sample Data
The module includes 10 sample authoring items with various:
- **Authoring Types**: Protocol, SOP, Report, Marketing, Patient Material, Training, Strategy
- **Statuses**: Draft, InReview, Approved, Published, Archived
- **Departments**: Clinical Research, Regulatory Affairs, Quality Assurance, etc.
- **Priorities**: High, Medium, Low
- **File Formats**: DOCX, PDF, PPTX

## 🚀 API Endpoints

### GET /api/authoring
Get paginated authoring items with filtering support

**Query Parameters:**
- `pageNumber` (int, default: 1) - Page number
- `pageSize` (int, default: 10, max: 100) - Items per page
- `searchTerm` (string, optional) - Search in title and description
- `statusFilter` (string, optional) - Filter by authoring status
- `categoryFilter` (string, optional) - Filter by category
- `authorFilter` (string, optional) - Filter by author
- `departmentFilter` (string, optional) - Filter by department

**Response:**
```json
{
  "success": true,
  "message": "Authoring items retrieved successfully",
  "data": {
    "items": [
      {
        "id": 1,
        "title": "Clinical Trial Protocol - Phase II Study",
        "description": "Protocol for the Phase II clinical trial...",
        "authoringType": "Protocol",
        "category": "Clinical",
        "author": "Dr. Jane Smith",
        "status": "Draft",
        "department": "Clinical Research",
        "priority": "High",
        "statusDisplay": "📝 Draft",
        "priorityDisplay": "🔴 High",
        "isOverdue": false,
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10,
    "totalCount": 10,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

### GET /api/authoring/health
Health check endpoint for the authoring module

## 🏗️ Architecture

### Modular Monolithic Structure
```
CIAPI.Modules.AuthoringTool/
├── Domain/
│   ├── Entities/
│   │   └── Authoring.cs             # Domain entity
│   └── Interfaces/
│       └── IAuthoringRepository.cs  # Repository interface
├── Application/
│   ├── DTOs/
│   │   └── AuthoringDto.cs          # Data transfer objects
│   └── Services/
│       ├── IAuthoringService.cs     # Service interface
│       └── AuthoringService.cs      # Service implementation
└── Infrastructure/
    ├── Data/
    │   ├── IDbConnectionFactory.cs  # Connection factory interface
    │   └── SqlServerConnectionFactory.cs # SQL Server implementation
    └── Repositories/
        └── AuthoringRepository.cs   # Repository implementation

CIAPI.Web/
└── Controllers/
    └── AuthoringController.cs       # API controller
```

## 🔧 Configuration

### Dependency Injection (Program.cs)
```csharp
// AuthoringTool Module Services
builder.Services.AddScoped<IAuthoringToolDbConnectionFactory, AuthoringToolSqlServerConnectionFactory>();
builder.Services.AddScoped<IAuthoringRepository, AuthoringRepository>();
builder.Services.AddScoped<IAuthoringService, AuthoringService>();
```

### Database Connection
Uses the same `DefaultConnection` connection string as other modules.

## 📝 Usage Examples

### Basic Authoring Retrieval
```bash
curl -X GET "http://localhost:5199/api/authoring?pageSize=5"
```

### Search Authoring Items
```bash
curl -X GET "http://localhost:5199/api/authoring?searchTerm=protocol&pageSize=10"
```

### Filter by Status
```bash
curl -X GET "http://localhost:5199/api/authoring?statusFilter=Draft&pageSize=10"
```

### Filter by Department
```bash
curl -X GET "http://localhost:5199/api/authoring?departmentFilter=Clinical%20Research&pageSize=10"
```

### Health Check
```bash
curl -X GET "http://localhost:5199/api/authoring/health"
```

## 🗂️ Authoring Status Workflow

1. **Draft** 📝 - Initial authoring item creation
2. **InReview** 👀 - Authoring item under review
3. **Approved** ✅ - Authoring item approved for publication
4. **Published** 🚀 - Authoring item published and available
5. **Archived** 📦 - Authoring item archived (historical)

## 🎨 Display Features

### Status Display
- Draft: 📝 Draft
- InReview: 👀 In Review
- Approved: ✅ Approved
- Published: 🚀 Published
- Archived: 📦 Archived

### Priority Display
- High: 🔴 High
- Medium: 🟡 Medium
- Low: 🟢 Low

### File Size Display
- Automatic conversion to B, KB, MB, GB
- Example: 2048000 bytes → "2.0 MB"

### Overdue Detection
- Automatically detects overdue authoring items based on DueDate
- Only applies to non-published/non-archived authoring items

## 🚀 Next Steps

1. **Authentication**: Add JWT authentication to secure endpoints
2. **Authorization**: Implement role-based access control
3. **File Upload**: Add file upload/download capabilities
4. **Version Control**: Implement document versioning
5. **Workflow**: Add approval workflow automation
6. **Notifications**: Add email notifications for reviews/approvals
7. **Full-text Search**: Implement document content search
8. **Unit Tests**: Implement comprehensive test coverage
9. **Integration Tests**: Test stored procedure integration
10. **Performance Monitoring**: Add application insights

---

**✅ Implementation Status: COMPLETE**

The GetAuthoring_Sample stored procedure has been successfully integrated into the CIAPI.Modules.AuthoringTool module with full async implementation and centralized API connectivity. All endpoints are working and ready for testing!
