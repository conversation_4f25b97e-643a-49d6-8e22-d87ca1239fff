using GDAPI.Modules.UserManagement.Domain.Entities;
using System.Linq.Expressions;

namespace GDAPI.Modules.UserManagement.Domain.Interfaces;

/// <summary>
/// Generic repository interface for common CRUD operations
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public interface IRepository<T> where T : BaseEntity
{
    // Query operations
    Task<T?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default);
    
    // Paging operations
    Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default);
    
    // Command operations
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    Task DeleteAsync(int id, CancellationToken cancellationToken = default);
    Task DeleteAsync(T entity, CancellationToken cancellationToken = default);
    Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    Task SoftDeleteAsync(int id, CancellationToken cancellationToken = default);
    Task SoftDeleteAsync(T entity, CancellationToken cancellationToken = default);
    
    // Transaction support
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// User-specific repository interface with additional user operations
/// </summary>
public interface IUserRepository : IRepository<User>
{
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<User?> GetByPhoneNumberAsync(string phoneNumber, CancellationToken cancellationToken = default);
    Task<bool> EmailExistsAsync(string email, int? excludeUserId = null, CancellationToken cancellationToken = default);
    Task<bool> PhoneExistsAsync(string phoneNumber, int? excludeUserId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName, CancellationToken cancellationToken = default);
    Task<IEnumerable<string>> GetUserRolesAsync(int userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<string>> GetUserPermissionsAsync(int userId, CancellationToken cancellationToken = default);
    Task<User?> GetUserWithRolesAsync(int userId, CancellationToken cancellationToken = default);
    Task UpdateLastLoginAsync(int userId, CancellationToken cancellationToken = default);
    Task IncrementAccessFailedCountAsync(int userId, CancellationToken cancellationToken = default);
    Task ResetAccessFailedCountAsync(int userId, CancellationToken cancellationToken = default);
    Task SetLockoutAsync(int userId, DateTime? lockoutEnd, CancellationToken cancellationToken = default);
}

/// <summary>
/// Role-specific repository interface
/// </summary>
public interface IRoleRepository : IRepository<Role>
{
    Task<Role?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<bool> RoleExistsAsync(string name, int? excludeRoleId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Role>> GetRolesWithPermissionsAsync(CancellationToken cancellationToken = default);
    Task<Role?> GetRoleWithPermissionsAsync(int roleId, CancellationToken cancellationToken = default);
    Task<IEnumerable<Permission>> GetRolePermissionsAsync(int roleId, CancellationToken cancellationToken = default);
    Task AddPermissionToRoleAsync(int roleId, int permissionId, CancellationToken cancellationToken = default);
    Task RemovePermissionFromRoleAsync(int roleId, int permissionId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Permission-specific repository interface
/// </summary>
public interface IPermissionRepository : IRepository<Permission>
{
    Task<Permission?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<Permission>> GetByModuleAsync(string module, CancellationToken cancellationToken = default);
    Task<bool> PermissionExistsAsync(string name, string module, string action, int? excludePermissionId = null, CancellationToken cancellationToken = default);
}
