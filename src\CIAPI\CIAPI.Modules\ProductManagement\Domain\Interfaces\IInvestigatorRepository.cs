using CIAPI.Modules.ProductManagement.Domain.Entities;

namespace CIAPI.Modules.ProductManagement.Domain.Interfaces;

/// <summary>
/// Investigator-specific repository interface for ProductManagement module
/// </summary>
public interface IInvestigatorRepository
{
    /// <summary>
    /// Get paginated investigators using stored procedure with filtering support
    /// </summary>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10)</param>
    /// <param name="searchTerm">Optional search term for investigator name</param>
    /// <param name="regionFilter">Optional region filter</param>
    /// <param name="countryFilter">Optional country filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated result of investigators with total count</returns>
    Task<(IEnumerable<Investigator> Items, int TotalCount)> GetInvestigatorsSamplePagedAsync(
        int pageNumber = 1,
        int pageSize = 10,
        string? searchTerm = null,
        string? regionFilter = null,
        string? countryFilter = null,
        CancellationToken cancellationToken = default);
}
