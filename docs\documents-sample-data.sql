-- =============================================
-- AuthoringTool Module - Sample Documents Data
-- =============================================

USE [CISolution]
GO

-- Insert sample documents if the table is empty
IF NOT EXISTS (SELECT TOP 1 * FROM Documents)
BEGIN
    PRINT 'Inserting sample document data...';
    
    -- Insert sample documents with various statuses, types, and departments
    INSERT INTO Documents (
        Title, 
        Description, 
        DocumentType, 
        Category, 
        Tags, 
        Author, 
        CoAuthors, 
        Status, 
        Version, 
        FileName, 
        FileSize, 
        FileFormat, 
        LastModified, 
        PublishedDate, 
        Department, 
        Project, 
        Priority, 
        DueDate, 
        ReviewComments, 
        ReviewedBy, 
        ReviewedDate, 
        ApprovedBy, 
        ApprovedDate, 
        CreatedBy, 
        IsActive
    )
    VALUES 
    -- Document 1: Draft
    (
        'Clinical Trial Protocol - Phase II Study', 
        'Protocol for the Phase II clinical trial of Drug XYZ for treatment of condition ABC', 
        'Protocol', 
        'Clinical', 
        'Phase II, Drug XYZ, ABC Disease', 
        'Dr. <PERSON>', 
        'Dr. <PERSON>, Dr. <PERSON>', 
        'Draft', 
        '0.1', 
        'Protocol_XYZ_Phase2_Draft.docx', 
        2048000, 
        'DOCX', 
        DATEADD(day, -5, GETUTCDATE()), 
        NULL, 
        'Clinical Research', 
        'XYZ Development', 
        'High', 
        DATEADD(day, 14, GETUTCDATE()), 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        'System', 
        1
    ),
    
    -- Document 2: In Review
    (
        'Regulatory Submission Guidelines', 
        'Guidelines for preparing regulatory submissions to FDA and EMA', 
        'SOP', 
        'Regulatory', 
        'FDA, EMA, Submission, Guidelines', 
        'Dr. Robert Chen', 
        'Sarah Williams', 
        'InReview', 
        '1.2', 
        'Regulatory_Submission_Guidelines_v1.2.pdf', 
        3145728, 
        'PDF', 
        DATEADD(day, -10, GETUTCDATE()), 
        NULL, 
        'Regulatory Affairs', 
        'Global Submissions', 
        'Medium', 
        DATEADD(day, 5, GETUTCDATE()), 
        'Please review section 3.2 regarding EMA requirements', 
        'Dr. Lisa Wong', 
        DATEADD(day, -2, GETUTCDATE()), 
        NULL, 
        NULL, 
        'System', 
        1
    ),
    
    -- Document 3: Approved
    (
        'Quality Control Procedures for Manufacturing', 
        'Standard operating procedures for quality control in the manufacturing process', 
        'SOP', 
        'Manufacturing', 
        'QC, Manufacturing, SOP', 
        'Michael Brown', 
        'Jennifer Lee', 
        'Approved', 
        '2.0', 
        'QC_Manufacturing_Procedures_v2.0.pdf', 
        5242880, 
        'PDF', 
        DATEADD(day, -30, GETUTCDATE()), 
        NULL, 
        'Quality Assurance', 
        'Manufacturing Excellence', 
        'High', 
        DATEADD(day, -5, GETUTCDATE()), 
        'Approved with minor changes to section 4.2', 
        'David Wilson', 
        DATEADD(day, -7, GETUTCDATE()), 
        'James Thompson', 
        DATEADD(day, -3, GETUTCDATE()), 
        'System', 
        1
    ),
    
    -- Document 4: Published
    (
        'Annual Safety Report 2023', 
        'Comprehensive safety report for all products for the year 2023', 
        'Report', 
        'Safety', 
        'Safety, Annual Report, 2023', 
        'Dr. Elizabeth Taylor', 
        NULL, 
        'Published', 
        '1.0', 
        'Annual_Safety_Report_2023.pdf', 
        10485760, 
        'PDF', 
        DATEADD(day, -45, GETUTCDATE()), 
        DATEADD(day, -40, GETUTCDATE()), 
        'Pharmacovigilance', 
        'Annual Reporting', 
        'Medium', 
        DATEADD(day, -42, GETUTCDATE()), 
        'Final report approved without changes', 
        'Dr. Richard Davis', 
        DATEADD(day, -43, GETUTCDATE()), 
        'Dr. Susan Miller', 
        DATEADD(day, -41, GETUTCDATE()), 
        'System', 
        1
    ),
    
    -- Document 5: Archived
    (
        'Marketing Materials for Product Launch 2022', 
        'Marketing materials and campaign strategy for the 2022 product launch', 
        'Marketing', 
        'Commercial', 
        'Marketing, Product Launch, 2022', 
        'Amanda Johnson', 
        'Thomas White, Jessica Brown', 
        'Archived', 
        '3.0', 
        'Marketing_Materials_2022_Final.pptx', 
        15728640, 
        'PPTX', 
        DATEADD(day, -365, GETUTCDATE()), 
        DATEADD(day, -360, GETUTCDATE()), 
        'Marketing', 
        'Product Launch 2022', 
        'Low', 
        DATEADD(day, -370, GETUTCDATE()), 
        'Approved and implemented successfully', 
        'Mark Robinson', 
        DATEADD(day, -368, GETUTCDATE()), 
        'Catherine Lewis', 
        DATEADD(day, -365, GETUTCDATE()), 
        'System', 
        1
    ),
    
    -- Document 6: Draft (Overdue)
    (
        'Patient Information Leaflet - Drug ABC', 
        'Information leaflet for patients describing usage, benefits and side effects of Drug ABC', 
        'Patient Material', 
        'Medical', 
        'PIL, Patient Information, Drug ABC', 
        'Dr. Sarah Johnson', 
        NULL, 
        'Draft', 
        '0.3', 
        'Patient_Leaflet_ABC_Draft.docx', 
        1048576, 
        'DOCX', 
        DATEADD(day, -20, GETUTCDATE()), 
        NULL, 
        'Medical Affairs', 
        'Patient Education', 
        'High', 
        DATEADD(day, -1, GETUTCDATE()), 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        'System', 
        1
    ),
    
    -- Document 7: In Review
    (
        'Clinical Study Report - Study XYZ-123', 
        'Final clinical study report for the XYZ-123 Phase III trial', 
        'Report', 
        'Clinical', 
        'CSR, Study XYZ-123, Phase III', 
        'Dr. William Harris', 
        'Dr. Emma Thompson, Dr. Robert Garcia', 
        'InReview', 
        '0.9', 
        'CSR_XYZ-123_Draft.docx', 
        8388608, 
        'DOCX', 
        DATEADD(day, -8, GETUTCDATE()), 
        NULL, 
        'Clinical Development', 
        'XYZ-123 Study', 
        'High', 
        DATEADD(day, 10, GETUTCDATE()), 
        'Please review statistical analysis section', 
        'Dr. Patricia Moore', 
        DATEADD(day, -3, GETUTCDATE()), 
        NULL, 
        NULL, 
        'System', 
        1
    ),
    
    -- Document 8: Approved
    (
        'Laboratory Standard Operating Procedures', 
        'Comprehensive SOPs for all laboratory operations and testing procedures', 
        'SOP', 
        'Laboratory', 
        'Lab, SOP, Testing', 
        'Dr. Kevin Wilson', 
        'Dr. Laura Martinez', 
        'Approved', 
        '2.1', 
        'Lab_SOP_v2.1.pdf', 
        4194304, 
        'PDF', 
        DATEADD(day, -15, GETUTCDATE()), 
        NULL, 
        'Laboratory Operations', 
        'Lab Excellence', 
        'Medium', 
        DATEADD(day, -5, GETUTCDATE()), 
        'Approved with updates to safety protocols', 
        'Dr. Michelle Lee', 
        DATEADD(day, -7, GETUTCDATE()), 
        'Dr. Jonathan Clark', 
        DATEADD(day, -5, GETUTCDATE()), 
        'System', 
        1
    ),
    
    -- Document 9: Published
    (
        'Employee Training Manual - GMP Compliance', 
        'Training manual for all employees on Good Manufacturing Practice compliance', 
        'Training', 
        'Compliance', 
        'GMP, Training, Compliance', 
        'Rachel Adams', 
        'Daniel Wright', 
        'Published', 
        '1.5', 
        'GMP_Training_Manual_v1.5.pdf', 
        7340032, 
        'PDF', 
        DATEADD(day, -60, GETUTCDATE()), 
        DATEADD(day, -55, GETUTCDATE()), 
        'Human Resources', 
        'Employee Training', 
        'Medium', 
        DATEADD(day, -58, GETUTCDATE()), 
        'Final version approved for company-wide distribution', 
        'Steven Jackson', 
        DATEADD(day, -57, GETUTCDATE()), 
        'Victoria Nelson', 
        DATEADD(day, -56, GETUTCDATE()), 
        'System', 
        1
    ),
    
    -- Document 10: Draft
    (
        'Product Development Strategy 2024-2025', 
        'Strategic plan for product development initiatives for the next two years', 
        'Strategy', 
        'R&D', 
        'Strategy, Product Development, 2024, 2025', 
        'Dr. Christopher Evans', 
        'Dr. Olivia Martin, James Wilson', 
        'Draft', 
        '0.2', 
        'Product_Dev_Strategy_2024-2025_Draft.pptx', 
        6291456, 
        'PPTX', 
        DATEADD(day, -3, GETUTCDATE()), 
        NULL, 
        'Research & Development', 
        'Strategic Planning', 
        'High', 
        DATEADD(day, 30, GETUTCDATE()), 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        'System', 
        1
    );
    
    PRINT 'Sample document data inserted successfully!';
END
ELSE
BEGIN
    PRINT 'Sample document data already exists.';
END
GO

-- Test the stored procedure
PRINT 'Testing GetDocuments_Sample stored procedure...';
EXEC GetDocuments_Sample;
GO

PRINT 'AuthoringTool module database setup completed successfully!';
GO
