{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A620C54B-7D1A-4483-BF97-E02170D80CC2}|src\\CIAPI\\CIAPI.Web\\CIAPI.Web.csproj|c:\\users\\<USER>\\documents\\augment-projects\\cisolution\\src\\ciapi\\ciapi.web\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A620C54B-7D1A-4483-BF97-E02170D80CC2}|src\\CIAPI\\CIAPI.Web\\CIAPI.Web.csproj|solutionrelative:src\\ciapi\\ciapi.web\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A620C54B-7D1A-4483-BF97-E02170D80CC2}|src\\CIAPI\\CIAPI.Web\\CIAPI.Web.csproj|c:\\users\\<USER>\\documents\\augment-projects\\cisolution\\src\\ciapi\\ciapi.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A620C54B-7D1A-4483-BF97-E02170D80CC2}|src\\CIAPI\\CIAPI.Web\\CIAPI.Web.csproj|solutionrelative:src\\ciapi\\ciapi.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A620C54B-7D1A-4483-BF97-E02170D80CC2}|src\\CIAPI\\CIAPI.Web\\CIAPI.Web.csproj|c:\\users\\<USER>\\documents\\augment-projects\\cisolution\\src\\ciapi\\ciapi.web\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A620C54B-7D1A-4483-BF97-E02170D80CC2}|src\\CIAPI\\CIAPI.Web\\CIAPI.Web.csproj|solutionrelative:src\\ciapi\\ciapi.web\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "AuthController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "src\\CIAPI\\CIAPI.Web\\Controllers\\AuthController.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\Controllers\\AuthController.cs", "RelativeToolTip": "src\\CIAPI\\CIAPI.Web\\Controllers\\AuthController.cs", "ViewState": "AgIAAIAAAAAAAAAAAADwv48AAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T03:43:41.373Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\appsettings.json", "RelativeDocumentMoniker": "src\\CIAPI\\CIAPI.Web\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\appsettings.json", "RelativeToolTip": "src\\CIAPI\\CIAPI.Web\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T09:21:00.797Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\Program.cs", "RelativeDocumentMoniker": "src\\CIAPI\\CIAPI.Web\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIAPI\\CIAPI.Web\\Program.cs", "RelativeToolTip": "src\\CIAPI\\CIAPI.Web\\Program.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAAAGgAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T06:20:15.654Z"}]}]}]}