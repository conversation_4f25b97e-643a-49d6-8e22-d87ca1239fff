namespace GDAPI.Shared.Authentication.Models;

/// <summary>
/// Configuration settings for API Key authentication
/// </summary>
public class ApiKeySettings
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "Authentication:ApiKey";

    /// <summary>
    /// Header name for API key (default: X-API-Key)
    /// </summary>
    public string HeaderName { get; set; } = "X-API-Key";

    /// <summary>
    /// Query parameter name for API key (optional)
    /// </summary>
    public string QueryParameterName { get; set; } = "apikey";

    /// <summary>
    /// Whether to allow API key in query parameter (default: false for security)
    /// </summary>
    public bool AllowQueryParameter { get; set; } = false;

    /// <summary>
    /// Whether to require HTTPS for API key authentication (default: true)
    /// </summary>
    public bool RequireHttps { get; set; } = true;

    /// <summary>
    /// Default rate limit per API key (requests per window)
    /// </summary>
    public int DefaultRateLimit { get; set; } = 1000;

    /// <summary>
    /// Rate limit window in minutes
    /// </summary>
    public int RateLimitWindowMinutes { get; set; } = 60;

    /// <summary>
    /// Whether rate limiting is enabled
    /// </summary>
    public bool EnableRateLimit { get; set; } = true;

    /// <summary>
    /// Master API key for initial setup (will be moved to database later)
    /// </summary>
    public string? MasterApiKey { get; set; }

    /// <summary>
    /// Whether to log API key usage
    /// </summary>
    public bool LogUsage { get; set; } = true;
}
