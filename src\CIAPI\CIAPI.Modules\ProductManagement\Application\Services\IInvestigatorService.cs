using CIAPI.Modules.ProductManagement.Application.DTOs;
using CIAPI.Shared.DTOs.Common;

namespace CIAPI.Modules.ProductManagement.Application.Services;

/// <summary>
/// Investigator service interface for investigator management operations
/// </summary>
public interface IInvestigatorService
{
    /// <summary>
    /// Get paginated investigators using stored procedure with filtering support
    /// </summary>
    /// <param name="request">Search request with pagination and filter parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated result of investigators</returns>
    Task<ApiResponse<PagedResult<InvestigatorDto>>> GetInvestigatorsSamplePagedAsync(InvestigatorSearchRequest request, CancellationToken cancellationToken = default);
}
