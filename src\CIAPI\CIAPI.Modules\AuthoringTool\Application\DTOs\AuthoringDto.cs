using GDAPI.Modules.AuthoringTool.Domain.Entities;
using System.ComponentModel.DataAnnotations;

namespace GDAPI.Modules.AuthoringTool.Application.DTOs;

/// <summary>
/// Authoring data transfer object for API responses
/// </summary>
public class AuthoringDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string AuthoringType { get; set; } = string.Empty;
    public string? Category { get; set; }
    public string? Tags { get; set; }
    public string Author { get; set; } = string.Empty;
    public string? CoAuthors { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? Version { get; set; }
    public string? FilePath { get; set; }
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public string? FileFormat { get; set; }
    public DateTime? LastModified { get; set; }
    public DateTime? PublishedDate { get; set; }
    public string? Department { get; set; }
    public string? Project { get; set; }
    public string? Priority { get; set; }
    public DateTime? DueDate { get; set; }
    public string? ReviewComments { get; set; }
    public string? ReviewedBy { get; set; }
    public DateTime? ReviewedDate { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Computed properties
    public string StatusDisplay { get; set; } = string.Empty;
    public string AuthorInfo { get; set; } = string.Empty;
    public string FileSizeDisplay { get; set; } = string.Empty;
    public bool IsOverdue { get; set; }
    public string PriorityDisplay { get; set; } = string.Empty;
}

/// <summary>
/// Create authoring request DTO
/// </summary>
public class CreateAuthoringRequest
{
    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string Title { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? Description { get; set; }

    [Required]
    [StringLength(50)]
    public string AuthoringType { get; set; } = string.Empty;

    [StringLength(100)]
    public string? Category { get; set; }

    [StringLength(500)]
    public string? Tags { get; set; }

    [Required]
    [StringLength(100)]
    public string Author { get; set; } = string.Empty;

    [StringLength(100)]
    public string? CoAuthors { get; set; }

    [StringLength(50)]
    public string Status { get; set; } = "Draft";

    [StringLength(20)]
    public string? Version { get; set; }

    [StringLength(100)]
    public string? Department { get; set; }

    [StringLength(100)]
    public string? Project { get; set; }

    [StringLength(50)]
    public string? Priority { get; set; }

    public DateTime? DueDate { get; set; }

    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Update authoring request DTO
/// </summary>
public class UpdateAuthoringRequest
{
    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string Title { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? Description { get; set; }

    [Required]
    [StringLength(50)]
    public string AuthoringType { get; set; } = string.Empty;

    [StringLength(100)]
    public string? Category { get; set; }

    [StringLength(500)]
    public string? Tags { get; set; }

    [StringLength(100)]
    public string? CoAuthors { get; set; }

    [StringLength(50)]
    public string Status { get; set; } = "Draft";

    [StringLength(20)]
    public string? Version { get; set; }

    [StringLength(100)]
    public string? Department { get; set; }

    [StringLength(100)]
    public string? Project { get; set; }

    [StringLength(50)]
    public string? Priority { get; set; }

    public DateTime? DueDate { get; set; }

    [StringLength(1000)]
    public string? ReviewComments { get; set; }

    public bool IsActive { get; set; }
}

/// <summary>
/// Authoring search request DTO
/// </summary>
public class AuthoringSearchRequest
{
    public string? SearchTerm { get; set; }
    public string? StatusFilter { get; set; }
    public string? CategoryFilter { get; set; }
    public string? AuthorFilter { get; set; }
    public string? DepartmentFilter { get; set; }
    public string? AuthoringTypeFilter { get; set; }
    public string? PriorityFilter { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Authoring filters DTO for dropdown lists
/// </summary>
public class AuthoringFiltersDto
{
    public IEnumerable<string> Statuses { get; set; } = new List<string>();
    public IEnumerable<string> Categories { get; set; } = new List<string>();
    public IEnumerable<string> Authors { get; set; } = new List<string>();
    public IEnumerable<string> Departments { get; set; } = new List<string>();
    public IEnumerable<string> AuthoringTypes { get; set; } = new List<string>();
    public IEnumerable<string> Priorities { get; set; } = new List<string>();
}

/// <summary>
/// Authoring statistics DTO
/// </summary>
public class AuthoringStatsDto
{
    public int TotalAuthoring { get; set; }
    public int DraftAuthoring { get; set; }
    public int InReviewAuthoring { get; set; }
    public int ApprovedAuthoring { get; set; }
    public int PublishedAuthoring { get; set; }
    public int OverdueAuthoring { get; set; }
    public int TotalAuthors { get; set; }
    public int TotalDepartments { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

public class UserDetailsDto
{
    public int Id { get; set; }
    public string ShortCode { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string EmailAddress { get; set; } = string.Empty;
    public string UserRole { get; set; } = string.Empty;
    public string JobTitle { get; set; } = string.Empty;
    public string LastLogin { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
}


public class ClinicalComparatorsGroupByIndicationsDto
{
    public string IndicationName { get; set; } = string.Empty;
    public List<ClinicalComparatorsEntryDto> Entries { get; set; } = new List<ClinicalComparatorsEntryDto>();
}

public class ClinicalComparatorsEntryDto
{
    public int Id { get; set; }
    public string VersionName { get; set; } = string.Empty;
    public ClinicalComparatorsStatus Status { get; set; }
    public bool IsDraftUpdate { get; set; }
    public string AccessType { get; set; } = string.Empty;
    public bool IsAccessTypeFirewalled { get; set; }
    public string OwnerInitials { get; set; } = string.Empty;
    public string OwnerName { get; set; } = string.Empty;
    public string LastModifiedDisplay { get; set; } = string.Empty; // Using a string for display simplicity
    public string? ClientName { get; set; }
    public bool IsClientFirewalled { get; set; }
}

public enum ClinicalComparatorsStatusDto
{
    Draft,
    Published
}