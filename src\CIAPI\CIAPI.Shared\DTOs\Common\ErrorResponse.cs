using System.Text.Json.Serialization;

namespace GDAPI.Shared.DTOs.Common;

/// <summary>
/// Detailed error response for exceptions
/// </summary>
public class ErrorResponse
{
    /// <summary>
    /// Error code for categorizing the error
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Human-readable error message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Additional error details (validation errors, context, etc.)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public object? Details { get; set; }

    /// <summary>
    /// Correlation ID for tracking the request
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? CorrelationId { get; set; }

    /// <summary>
    /// Module where the error occurred
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Module { get; set; }

    /// <summary>
    /// Timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Stack trace (only in development environment)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? StackTrace { get; set; }

    /// <summary>
    /// Inner exception details (only in development environment)
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public ErrorResponse? InnerError { get; set; }

    /// <summary>
    /// Additional context information
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public Dictionary<string, object>? Context { get; set; }
}

/// <summary>
/// Error log entry for database storage
/// </summary>
public class ErrorLogEntry
{
    public int Id { get; set; }
    public string ErrorCode { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? StackTrace { get; set; }
    public string? InnerException { get; set; }
    public string? CorrelationId { get; set; }
    public string? Module { get; set; }
    public string? UserId { get; set; }
    public string? RequestPath { get; set; }
    public string? HttpMethod { get; set; }
    public string? UserAgent { get; set; }
    public string? IpAddress { get; set; }
    public string? Details { get; set; }
    public string? Context { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string Severity { get; set; } = "Error";
    public string? Environment { get; set; }
    public string? MachineName { get; set; }
    public string? ProcessId { get; set; }
    public string? ThreadId { get; set; }
}
