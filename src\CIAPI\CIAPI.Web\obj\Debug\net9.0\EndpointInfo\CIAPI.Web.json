{"openapi": "3.0.4", "info": {"title": "CI API", "description": "Centralized API for CI Solution - A Modern Modular Monolithic Architecture", "contact": {"name": "CI Solution Team", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Auth/generate-key": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateApiKeyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GenerateApiKeyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GenerateApiKeyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateApiKeyResponseApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Auth/validate-key": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateApiKeyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidateApiKeyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidateApiKeyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyValidationResponseApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Auth/revoke-key": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevokeApiKeyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RevokeApiKeyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RevokeApiKeyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Auth/keys": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyInfoIEnumerableApiResponse"}}}}}}}, "/api/Auth/current": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyInfoApiResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Authoring": {"get": {"tags": ["Authoring"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "statusFilter", "in": "query", "schema": {"type": "string"}}, {"name": "categoryFilter", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>er", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthoringDtoPagedResultApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Authoring/health": {"get": {"tags": ["Authoring"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {}}}}}}}, "/api/Authoring/users": {"get": {"tags": ["Authoring"], "parameters": [{"name": "type", "in": "query", "schema": {"type": "string", "default": "All"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDetailsDtoListApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Authoring/clinical-comparators": {"get": {"tags": ["Authoring"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicalComparatorsGroupByIndicationsDtoIEnumerableApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/health": {"get": {"tags": ["Health"], "operationId": "HealthCheck", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringDateTime<>f__AnonymousType1"}}}}}}}, "/api/Investigators": {"get": {"tags": ["Investigators"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "regionFilter", "in": "query", "schema": {"type": "string"}}, {"name": "countryF<PERSON>er", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvestigatorDtoPagedResultApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Investigators/health": {"get": {"tags": ["Investigators"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"ApiKeyInfo": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "clientName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "rateLimit": {"type": "integer", "format": "int32"}, "rateLimitWindowMinutes": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "lastUsedAt": {"type": "string", "format": "date-time", "nullable": true}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "ApiKeyInfoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ApiKeyInfo"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiKeyInfoIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ApiKeyInfo"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiKeyValidationResponse": {"type": "object", "properties": {"isValid": {"type": "boolean"}, "errorMessage": {"type": "string", "nullable": true}, "rateLimitExceeded": {"type": "boolean"}, "remainingRequests": {"type": "integer", "format": "int32"}, "rateLimitResetTime": {"type": "string", "format": "date-time", "nullable": true}, "apiKeyInfo": {"$ref": "#/components/schemas/ApiKeyInfo"}}, "additionalProperties": false}, "ApiKeyValidationResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ApiKeyValidationResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthoringDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "authoringType": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "tags": {"type": "string", "nullable": true}, "author": {"type": "string", "nullable": true}, "coAuthors": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64", "nullable": true}, "fileFormat": {"type": "string", "nullable": true}, "lastModified": {"type": "string", "format": "date-time", "nullable": true}, "publishedDate": {"type": "string", "format": "date-time", "nullable": true}, "department": {"type": "string", "nullable": true}, "project": {"type": "string", "nullable": true}, "priority": {"type": "string", "nullable": true}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "reviewComments": {"type": "string", "nullable": true}, "reviewedBy": {"type": "string", "nullable": true}, "reviewedDate": {"type": "string", "format": "date-time", "nullable": true}, "approvedBy": {"type": "string", "nullable": true}, "approvedDate": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "statusDisplay": {"type": "string", "nullable": true}, "authorInfo": {"type": "string", "nullable": true}, "fileSizeDisplay": {"type": "string", "nullable": true}, "isOverdue": {"type": "boolean"}, "priorityDisplay": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthoringDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AuthoringDto"}, "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}, "previousPageNumber": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "nextPageNumber": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "AuthoringDtoPagedResultApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/AuthoringDtoPagedResult"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BooleanApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicalComparatorsEntryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "versionName": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/ClinicalComparatorsStatus"}, "isDraftUpdate": {"type": "boolean"}, "accessType": {"type": "string", "nullable": true}, "isAccessTypeFirewalled": {"type": "boolean"}, "ownerInitials": {"type": "string", "nullable": true}, "ownerName": {"type": "string", "nullable": true}, "lastModifiedDisplay": {"type": "string", "nullable": true}, "clientName": {"type": "string", "nullable": true}, "isClientFirewalled": {"type": "boolean"}}, "additionalProperties": false}, "ClinicalComparatorsGroupByIndicationsDto": {"type": "object", "properties": {"indicationName": {"type": "string", "nullable": true}, "entries": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicalComparatorsEntryDto"}, "nullable": true}}, "additionalProperties": false}, "ClinicalComparatorsGroupByIndicationsDtoIEnumerableApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicalComparatorsGroupByIndicationsDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicalComparatorsStatus": {"enum": [0, 1], "type": "integer", "format": "int32"}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "details": {"nullable": true}, "correlationId": {"type": "string", "nullable": true}, "module": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "stackTrace": {"type": "string", "nullable": true}, "innerError": {"$ref": "#/components/schemas/ErrorResponse"}, "context": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "GenerateApiKeyRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "clientName": {"type": "string", "nullable": true}, "rateLimit": {"type": "integer", "format": "int32", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GenerateApiKeyResponse": {"type": "object", "properties": {"apiKey": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "clientName": {"type": "string", "nullable": true}, "rateLimit": {"type": "integer", "format": "int32"}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "GenerateApiKeyResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/GenerateApiKeyResponse"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InvestigatorDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "investigatorName": {"type": "string", "nullable": true}, "specializationNames": {"type": "string", "nullable": true}, "designation": {"type": "string", "nullable": true}, "organisation": {"type": "string", "nullable": true}, "contactNumber": {"type": "string", "nullable": true}, "emailID": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "regionName": {"type": "string", "nullable": true}, "countryName": {"type": "string", "nullable": true}, "stateName": {"type": "string", "nullable": true}, "cityName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "fullLocation": {"type": "string", "nullable": true}, "contactInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "InvestigatorDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/InvestigatorDto"}, "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}, "previousPageNumber": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}, "nextPageNumber": {"type": "integer", "format": "int32", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "InvestigatorDtoPagedResultApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/InvestigatorDtoPagedResult"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RevokeApiKeyRequest": {"type": "object", "properties": {"apiKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringDateTime<>f__AnonymousType1": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserDetailsDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "shortCode": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "userRole": {"type": "string", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "lastLogin": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDetailsDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserDetailsDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "error": {"$ref": "#/components/schemas/ErrorResponse"}, "timestamp": {"type": "string", "format": "date-time"}, "traceId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ValidateApiKeyRequest": {"type": "object", "properties": {"apiKey": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"ApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Key authentication. Add your API key in the X-API-Key header. Example: \"X-API-Key: your-api-key\"", "name": "X-API-Key", "in": "header"}}}, "security": [{"ApiKey": []}]}