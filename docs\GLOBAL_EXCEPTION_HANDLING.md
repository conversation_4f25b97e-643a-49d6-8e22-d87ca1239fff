# Global Exception Handling System

## 🎯 Overview

The CI Solution implements a comprehensive global exception handling system that provides:

- **Centralized Exception Management**: All exceptions are handled consistently across all modules
- **Dual Logging**: Parallel logging to both file system and database
- **Custom Exception Types**: Structured exception hierarchy for different error scenarios
- **Detailed Error Responses**: Rich error information with correlation IDs and context
- **Production-Ready**: Environment-aware error details and security considerations

## 🏗️ Architecture

### Components

```
CIAPI.Shared/
├── Exceptions/                    # Custom exception classes
│   ├── BaseException.cs          # Base class for all custom exceptions
│   ├── BusinessException.cs      # Business logic violations
│   ├── ValidationException.cs    # Input validation errors
│   ├── NotFoundException.cs      # Resource not found errors
│   ├── UnauthorizedException.cs  # Authentication/authorization errors
│   └── DatabaseException.cs      # Database-related errors
├── Middleware/                   # Exception handling middleware
│   ├── GlobalExceptionMiddleware.cs
│   └── ExceptionMiddlewareExtensions.cs
├── Services/                     # Error logging services
│   ├── IErrorLoggingService.cs
│   └── ErrorLoggingService.cs
└── DTOs/Common/                  # Response DTOs
    ├── ErrorResponse.cs
    └── ApiResponse.cs (updated)
```

## 🚀 Features

### ✅ **Centralized Exception Handling**
- Single middleware handles all unhandled exceptions
- Consistent error response format across all endpoints
- Automatic correlation ID generation and tracking

### ✅ **Dual Logging System**
- **File Logging**: JSON-formatted logs with daily rotation
- **Database Logging**: Structured storage with full-text search capabilities
- **Parallel Processing**: Both logging methods run simultaneously for reliability

### ✅ **Rich Exception Types**
- **BaseException**: Foundation with correlation ID, module, and context support
- **BusinessException**: For business rule violations (400 Bad Request)
- **ValidationException**: For input validation errors with field-level details
- **NotFoundException**: For missing resources with resource type tracking
- **DatabaseException**: For database errors with SQL error details
- **UnauthorizedException/ForbiddenException**: For security violations

### ✅ **Context-Aware Logging**
- Request path, HTTP method, user agent, IP address
- Correlation IDs for request tracking
- Module identification for error source tracking
- Custom context data for debugging

### ✅ **Environment-Aware Responses**
- **Development**: Full stack traces and inner exception details
- **Production**: User-friendly messages without sensitive information
- **Security**: No internal system details exposed to clients

## 📊 Database Schema

### ErrorLogs Table
```sql
CREATE TABLE ErrorLogs (
    Id int IDENTITY(1,1) PRIMARY KEY,
    ErrorCode nvarchar(100) NOT NULL,
    Message nvarchar(2000) NOT NULL,
    StackTrace nvarchar(MAX) NULL,
    InnerException nvarchar(MAX) NULL,
    CorrelationId nvarchar(100) NULL,
    Module nvarchar(100) NULL,
    UserId nvarchar(100) NULL,
    RequestPath nvarchar(500) NULL,
    HttpMethod nvarchar(10) NULL,
    UserAgent nvarchar(1000) NULL,
    IpAddress nvarchar(50) NULL,
    Details nvarchar(MAX) NULL,
    Context nvarchar(MAX) NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    Severity nvarchar(20) NOT NULL DEFAULT 'Error',
    Environment nvarchar(50) NULL,
    MachineName nvarchar(100) NULL,
    ProcessId nvarchar(20) NULL,
    ThreadId nvarchar(20) NULL
);
```

### Stored Procedures
- **InsertErrorLog**: Insert new error log entries
- **GetErrorLogs**: Retrieve error logs with filtering and pagination
- **CleanupErrorLogs**: Remove old error logs based on retention policy

## 🔧 Setup Instructions

### 1. Database Setup
```sql
-- Run the database schema script
-- File: docs/error-logging-schema.sql
```

### 2. Application Configuration
The system is already configured in `Program.cs`:

```csharp
// Register services
builder.Services.AddGlobalExceptionHandling();

// Register middleware (first in pipeline)
app.UseGlobalExceptionHandling();
```

### 3. Configuration Settings
Add to `appsettings.json`:

```json
{
  "Logging": {
    "ErrorLogDirectory": "Logs"
  },
  "ConnectionStrings": {
    "DefaultConnection": "your-connection-string"
  }
}
```

## 💻 Usage Examples

### Using Custom Exceptions in Services

```csharp
public async Task<AuthoringDto> GetAuthoringByIdAsync(int id)
{
    // Validation
    if (id <= 0)
        throw new ValidationException("ID must be greater than 0")
            .SetModule("AuthoringTool");

    var authoring = await _repository.GetByIdAsync(id);
    
    // Not found
    if (authoring == null)
        throw NotFoundException.ForResource("Authoring", id.ToString())
            .SetModule("AuthoringTool");

    return MapToDto(authoring);
}
```

### Business Logic Validation

```csharp
public async Task UpdateAuthoringStatusAsync(int id, string status)
{
    var validStatuses = new[] { "Draft", "InReview", "Approved", "Published" };
    
    if (!validStatuses.Contains(status))
        throw new BusinessException($"Invalid status '{status}'. Valid statuses are: {string.Join(", ", validStatuses)}")
            .SetModule("AuthoringTool")
            .AddContext("ProvidedStatus", status)
            .AddContext("ValidStatuses", validStatuses);
}
```

### Controller Implementation

```csharp
[HttpGet("{id}")]
public async Task<ActionResult<ApiResponse<AuthoringDto>>> GetAuthoring(int id)
{
    // No try-catch needed - middleware handles exceptions
    var result = await _authoringService.GetAuthoringByIdAsync(id);
    return Ok(ApiResponse<AuthoringDto>.SuccessResult(result));
}
```

## 📋 Error Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Authoring item with ID '123' was not found",
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "Authoring item with ID '123' was not found",
    "details": {
      "resourceType": "Authoring",
      "resourceId": "123"
    },
    "correlationId": "abc-123-def-456",
    "module": "AuthoringTool",
    "timestamp": "2024-01-20T15:45:30Z"
  },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

### Validation Error Response
```json
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "One or more validation errors occurred",
    "details": {
      "pageNumber": ["Page number must be greater than 0"],
      "pageSize": ["Page size must be between 1 and 100"]
    },
    "correlationId": "abc-123-def-456",
    "timestamp": "2024-01-20T15:45:30Z"
  },
  "timestamp": "2024-01-20T15:45:30Z"
}
```

## 📁 File Logging Format

Logs are written to `Logs/errors-{date}.log` in JSON format:

```json
{
  "timestamp": "2024-01-20T15:45:30Z",
  "severity": "Error",
  "errorCode": "RESOURCE_NOT_FOUND",
  "message": "Authoring item with ID '123' was not found",
  "correlationId": "abc-123-def-456",
  "module": "AuthoringTool",
  "requestPath": "/api/authoring/123",
  "httpMethod": "GET",
  "ipAddress": "*************",
  "userAgent": "Mozilla/5.0...",
  "environment": "Development",
  "machineName": "DEV-SERVER-01"
}
```

## 🔍 Monitoring and Maintenance

### Error Log Queries

```sql
-- Get recent errors
SELECT TOP 100 * FROM ErrorLogs 
ORDER BY CreatedAt DESC;

-- Get errors by module
SELECT * FROM ErrorLogs 
WHERE Module = 'AuthoringTool' 
ORDER BY CreatedAt DESC;

-- Get errors by correlation ID
SELECT * FROM ErrorLogs 
WHERE CorrelationId = 'abc-123-def-456';
```

### Cleanup Old Logs

```sql
-- Clean up logs older than 90 days
EXEC CleanupErrorLogs @RetentionDays = 90;
```

## 🎯 Best Practices

### ✅ **DO:**
- Use specific exception types for different scenarios
- Add context information to exceptions
- Set module names for better tracking
- Let the middleware handle all exceptions
- Use correlation IDs for request tracking

### ❌ **DON'T:**
- Catch and re-throw exceptions unnecessarily
- Log exceptions in controllers (middleware handles this)
- Expose sensitive information in error messages
- Use generic Exception class for business logic errors

## 🚨 Security Considerations

- Stack traces only shown in development environment
- Sensitive data filtered from error responses
- SQL injection protection in error logging
- Rate limiting on error endpoints (recommended)
- Regular log cleanup to prevent disk space issues

---

**✅ IMPLEMENTATION COMPLETE!**

The Global Exception Handling system is now fully implemented and ready for production use across all modules in the CI Solution.
