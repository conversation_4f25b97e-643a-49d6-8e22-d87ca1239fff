{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CISolutionDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "CIUploadDatabaseConnection": "Data Source=************,1533;Initial Catalog=Pharma_v2;User ID=HCare_Dev;Password=*****$f8v62sxr33b3#;Application Name=SitePlatformContent;TrustServerCertificate=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "Authentication": {"ApiKey": {"HeaderName": "X-API-Key", "QueryParameterName": "apikey", "AllowQueryParameter": false, "RequireHttps": false, "DefaultRateLimit": 1000, "RateLimitWindowMinutes": 60, "EnableRateLimit": true, "MasterApiKey": "nYQkMFAYhM4zXMTZcYV8RwFFXKnBlOIffO6DqQ", "LogUsage": true}}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "CISolution", "Audience": "CISolution", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SenderEmail": "<EMAIL>", "SenderName": "CI Solution", "Username": "", "Password": "", "EnableSsl": true}, "SmsSettings": {"Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "", "AuthToken": "", "FromPhoneNumber": ""}, "ApplicationSettings": {"ApplicationName": "CI Solution", "ApplicationVersion": "1.0.0", "Environment": "Development", "EnableDetailedErrors": true, "MaxLoginAttempts": 5, "LockoutDurationInMinutes": 30}}