/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-eurpwzwu3o] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-eurpwzwu3o] {
  color: #0077cc;
}

.btn-primary[b-eurpwzwu3o] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-eurpwzwu3o], .nav-pills .show > .nav-link[b-eurpwzwu3o] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-eurpwzwu3o] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-eurpwzwu3o] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-eurpwzwu3o] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-eurpwzwu3o] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-eurpwzwu3o] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
