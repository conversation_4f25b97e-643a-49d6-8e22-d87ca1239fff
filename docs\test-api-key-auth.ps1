# API Key Authentication Testing Script
# This script tests the API key authentication system

param(
    [string]$BaseUrl = "http://localhost:5199",
    [string]$ApiKey = "ci-solution-beta-key-2024",
    [switch]$Verbose
)

Write-Host "🔑 Testing API Key Authentication System" -ForegroundColor Cyan
Write-Host "Base URL: $BaseUrl" -ForegroundColor Gray
Write-Host "API Key: $ApiKey" -ForegroundColor Gray
Write-Host ""

# Function to make HTTP request and display results
function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Description,
        [string]$ApiKeyHeader = $null,
        [int]$ExpectedStatusCode = 200,
        [string]$Method = "GET",
        [string]$Body = $null
    )
    
    Write-Host "Testing: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $headers = @{
            'Content-Type' = 'application/json'
        }
        
        if ($ApiKeyHeader) {
            $headers['X-API-Key'] = $ApiKeyHeader
            Write-Host "API Key: $ApiKeyHeader" -ForegroundColor Gray
        } else {
            Write-Host "API Key: (none)" -ForegroundColor Gray
        }
        
        $requestParams = @{
            Uri = $Url
            Method = $Method
            Headers = $headers
            UseBasicParsing = $true
            ErrorAction = 'SilentlyContinue'
        }
        
        if ($Body) {
            $requestParams.Body = $Body
        }
        
        $response = Invoke-WebRequest @requestParams
        $statusCode = $response.StatusCode
        $content = $response.Content | ConvertFrom-Json
        
        Write-Host "Status: $statusCode" -ForegroundColor $(if ($statusCode -eq $ExpectedStatusCode) { "Green" } else { "Red" })
        
        # Display rate limit headers if present
        if ($response.Headers['X-RateLimit-Limit']) {
            Write-Host "Rate Limit: $($response.Headers['X-RateLimit-Limit'])" -ForegroundColor Cyan
            Write-Host "Remaining: $($response.Headers['X-RateLimit-Remaining'])" -ForegroundColor Cyan
        }
        
        if ($Verbose) {
            Write-Host "Response:" -ForegroundColor Gray
            $content | ConvertTo-Json -Depth 10 | Write-Host -ForegroundColor Gray
        } else {
            Write-Host "Success: $($content.success)" -ForegroundColor $(if ($content.success) { "Green" } else { "Red" })
            Write-Host "Message: $($content.message)" -ForegroundColor Gray
            if ($content.error) {
                Write-Host "Error Code: $($content.error.code)" -ForegroundColor Red
            }
        }
    }
    catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $responseBody = $reader.ReadToEnd()
        
        Write-Host "Status: $statusCode" -ForegroundColor $(if ($statusCode -eq $ExpectedStatusCode) { "Green" } else { "Red" })
        
        if ($responseBody) {
            try {
                $content = $responseBody | ConvertFrom-Json
                if ($Verbose) {
                    Write-Host "Response:" -ForegroundColor Gray
                    $content | ConvertTo-Json -Depth 10 | Write-Host -ForegroundColor Gray
                } else {
                    Write-Host "Success: $($content.success)" -ForegroundColor $(if ($content.success) { "Green" } else { "Red" })
                    Write-Host "Message: $($content.message)" -ForegroundColor Gray
                    if ($content.error) {
                        Write-Host "Error Code: $($content.error.code)" -ForegroundColor Red
                    }
                }
            }
            catch {
                Write-Host "Raw Response: $responseBody" -ForegroundColor Red
            }
        }
    }
    
    Write-Host ""
    Start-Sleep -Seconds 1
}

# Test 1: Public endpoint (should work without API key)
Write-Host "=== Test 1: Public Endpoint (No API Key Required) ===" -ForegroundColor Green
Test-ApiEndpoint -Url "$BaseUrl/swagger/index.html" -Description "Swagger UI (public endpoint)" -ExpectedStatusCode 200

# Test 2: Protected endpoint without API key (should fail)
Write-Host "=== Test 2: Protected Endpoint Without API Key ===" -ForegroundColor Green
Test-ApiEndpoint -Url "$BaseUrl/api/authoring" -Description "Get authoring items (no API key)" -ExpectedStatusCode 401

# Test 3: Protected endpoint with invalid API key (should fail)
Write-Host "=== Test 3: Protected Endpoint With Invalid API Key ===" -ForegroundColor Green
Test-ApiEndpoint -Url "$BaseUrl/api/authoring" -Description "Get authoring items (invalid API key)" -ApiKeyHeader "invalid-key-123" -ExpectedStatusCode 401

# Test 4: Protected endpoint with valid API key (should succeed)
Write-Host "=== Test 4: Protected Endpoint With Valid API Key ===" -ForegroundColor Green
Test-ApiEndpoint -Url "$BaseUrl/api/authoring" -Description "Get authoring items (valid API key)" -ApiKeyHeader $ApiKey -ExpectedStatusCode 200

# Test 5: Health check with valid API key
Write-Host "=== Test 5: Health Check With Valid API Key ===" -ForegroundColor Green
Test-ApiEndpoint -Url "$BaseUrl/api/authoring/health" -Description "Health check (valid API key)" -ApiKeyHeader $ApiKey -ExpectedStatusCode 200

# Test 6: Generate new API key (public endpoint)
Write-Host "=== Test 6: Generate New API Key ===" -ForegroundColor Green
$generateKeyBody = @{
    name = "Test Key"
    clientName = "PowerShell Test Script"
    rateLimit = 100
} | ConvertTo-Json

Test-ApiEndpoint -Url "$BaseUrl/api/auth/generate-key" -Description "Generate new API key" -Method "POST" -Body $generateKeyBody -ExpectedStatusCode 200

# Test 7: Validate API key (public endpoint)
Write-Host "=== Test 7: Validate API Key ===" -ForegroundColor Green
$validateKeyBody = @{
    apiKey = $ApiKey
} | ConvertTo-Json

Test-ApiEndpoint -Url "$BaseUrl/api/auth/validate-key" -Description "Validate API key" -Method "POST" -Body $validateKeyBody -ExpectedStatusCode 200

# Test 8: Get current API key info (protected endpoint)
Write-Host "=== Test 8: Get Current API Key Info ===" -ForegroundColor Green
Test-ApiEndpoint -Url "$BaseUrl/api/auth/current" -Description "Get current API key info" -ApiKeyHeader $ApiKey -ExpectedStatusCode 200

# Test 9: List all API keys (protected endpoint)
Write-Host "=== Test 9: List All API Keys ===" -ForegroundColor Green
Test-ApiEndpoint -Url "$BaseUrl/api/auth/keys" -Description "List all API keys" -ApiKeyHeader $ApiKey -ExpectedStatusCode 200

# Test 10: Rate limiting test (make multiple requests quickly)
Write-Host "=== Test 10: Rate Limiting Test ===" -ForegroundColor Green
Write-Host "Making 10 quick requests to test rate limiting..." -ForegroundColor Yellow

for ($i = 1; $i -le 10; $i++) {
    Write-Host "Request $i/10" -ForegroundColor Gray
    Test-ApiEndpoint -Url "$BaseUrl/api/authoring/health" -Description "Rate limit test request $i" -ApiKeyHeader $ApiKey -ExpectedStatusCode 200
}

Write-Host "🎉 API Key Authentication Tests Completed!" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Yellow
Write-Host "- Public endpoints should work without API keys" -ForegroundColor Gray
Write-Host "- Protected endpoints should require valid API keys" -ForegroundColor Gray
Write-Host "- Invalid API keys should return 401 Unauthorized" -ForegroundColor Gray
Write-Host "- Rate limiting headers should be present in responses" -ForegroundColor Gray
Write-Host ""
Write-Host "🔑 Your Master API Key:" -ForegroundColor Yellow
Write-Host "$ApiKey" -ForegroundColor Green
Write-Host ""
Write-Host "📖 Usage Examples:" -ForegroundColor Yellow
Write-Host "curl -H 'X-API-Key: $ApiKey' $BaseUrl/api/authoring" -ForegroundColor Gray
Write-Host "curl -H 'X-API-Key: $ApiKey' $BaseUrl/api/authoring/health" -ForegroundColor Gray
Write-Host ""
Write-Host "🔍 To run with verbose output:" -ForegroundColor Yellow
Write-Host ".\test-api-key-auth.ps1 -Verbose" -ForegroundColor Gray
Write-Host ""
Write-Host "📚 Documentation: docs/API_KEY_AUTHENTICATION.md" -ForegroundColor Yellow
