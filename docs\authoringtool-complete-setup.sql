-- =============================================
-- AuthoringTool Module - Complete Database Setup
-- This script creates the Documents table, stored procedure, and sample data
-- =============================================

USE [CISolution]
GO

PRINT 'Starting AuthoringTool Module Database Setup...';
GO

-- =============================================
-- 1. CREATE AUTHORING TABLE
-- =============================================

-- Create Authoring table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Authoring' AND xtype='U')
BEGIN
    CREATE TABLE Authoring (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Title nvarchar(200) NOT NULL,
        Description nvarchar(1000) NULL,
        AuthoringType nvarchar(50) NOT NULL,
        Category nvarchar(100) NULL,
        Tags nvarchar(500) NULL,
        Author nvarchar(100) NOT NULL,
        CoAuthors nvarchar(100) NULL,
        Status nvarchar(50) NOT NULL DEFAULT 'Draft',
        Version nvarchar(20) NULL,
        FilePath nvarchar(500) NULL,
        FileName nvarchar(100) NULL,
        FileSize bigint NULL,
        FileFormat nvarchar(50) NULL,
        LastModified datetime2 NULL,
        PublishedDate datetime2 NULL,
        Department nvarchar(100) NULL,
        Project nvarchar(100) NULL,
        Priority nvarchar(50) NULL,
        DueDate datetime2 NULL,
        ReviewComments nvarchar(1000) NULL,
        ReviewedBy nvarchar(100) NULL,
        ReviewedDate datetime2 NULL,
        ApprovedBy nvarchar(100) NULL,
        ApprovedDate datetime2 NULL,
        CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt datetime2 NULL,
        CreatedBy nvarchar(100) NULL,
        UpdatedBy nvarchar(100) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        IsDeleted bit NOT NULL DEFAULT 0,
        RowVersion rowversion
    );

    PRINT 'Authoring table created successfully!';
END
ELSE
BEGIN
    PRINT 'Authoring table already exists.';
END
GO

-- =============================================
-- 2. CREATE INDEXES
-- =============================================

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Authoring_Title')
BEGIN
    CREATE INDEX IX_Authoring_Title ON Authoring(Title);
    PRINT 'Index IX_Authoring_Title created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Authoring_Author')
BEGIN
    CREATE INDEX IX_Authoring_Author ON Authoring(Author);
    PRINT 'Index IX_Authoring_Author created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Authoring_Status')
BEGIN
    CREATE INDEX IX_Authoring_Status ON Authoring(Status);
    PRINT 'Index IX_Authoring_Status created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Authoring_Department')
BEGIN
    CREATE INDEX IX_Authoring_Department ON Authoring(Department);
    PRINT 'Index IX_Authoring_Department created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Authoring_Category')
BEGIN
    CREATE INDEX IX_Authoring_Category ON Authoring(Category);
    PRINT 'Index IX_Authoring_Category created.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Authoring_CreatedAt')
BEGIN
    CREATE INDEX IX_Authoring_CreatedAt ON Authoring(CreatedAt);
    PRINT 'Index IX_Authoring_CreatedAt created.';
END
GO

-- =============================================
-- 3. CREATE STORED PROCEDURE
-- =============================================

-- Create the GetAuthoring_Sample stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'GetAuthoring_Sample')
BEGIN
    DROP PROCEDURE GetAuthoring_Sample;
    PRINT 'Existing GetAuthoring_Sample procedure dropped.';
END
GO

CREATE PROCEDURE GetAuthoring_Sample
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Return all authoring items with the specified columns
        SELECT
            Id,
            Title,
            Description,
            AuthoringType,
            Category,
            Tags,
            Author,
            CoAuthors,
            Status,
            Version,
            FilePath,
            FileName,
            FileSize,
            FileFormat,
            LastModified,
            PublishedDate,
            Department,
            Project,
            Priority,
            DueDate,
            ReviewComments,
            ReviewedBy,
            ReviewedDate,
            ApprovedBy,
            ApprovedDate,
            CreatedAt,
            UpdatedAt,
            CreatedBy,
            UpdatedBy,
            IsActive,
            IsDeleted,
            RowVersion
        FROM Authoring
        WHERE IsDeleted = 0
        ORDER BY CreatedAt DESC;

    END TRY
    BEGIN CATCH
        -- Error handling
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'GetAuthoring_Sample stored procedure created successfully!';
GO

-- =============================================
-- 4. INSERT SAMPLE DATA
-- =============================================

-- Insert sample authoring items if the table is empty
IF NOT EXISTS (SELECT TOP 1 * FROM Authoring)
BEGIN
    PRINT 'Inserting sample authoring data...';

    -- Insert sample authoring items with various statuses, types, and departments
    INSERT INTO Authoring (
        Title,
        Description,
        AuthoringType,
        Category,
        Tags,
        Author,
        CoAuthors,
        Status,
        Version,
        FileName,
        FileSize,
        FileFormat,
        LastModified,
        PublishedDate,
        Department,
        Project,
        Priority,
        DueDate,
        ReviewComments,
        ReviewedBy,
        ReviewedDate,
        ApprovedBy,
        ApprovedDate,
        CreatedBy,
        IsActive
    )
    VALUES
    -- Authoring Item 1: Draft
    (
        'Clinical Trial Protocol - Phase II Study',
        'Protocol for the Phase II clinical trial of Drug XYZ for treatment of condition ABC',
        'Protocol',
        'Clinical',
        'Phase II, Drug XYZ, ABC Disease',
        'Dr. Jane Smith',
        'Dr. John Doe, Dr. Emily Johnson',
        'Draft',
        '0.1',
        'Protocol_XYZ_Phase2_Draft.docx',
        2048000,
        'DOCX',
        DATEADD(day, -5, GETUTCDATE()),
        NULL,
        'Clinical Research',
        'XYZ Development',
        'High',
        DATEADD(day, 14, GETUTCDATE()),
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        'System',
        1
    ),

    -- Authoring Item 2: In Review
    (
        'Regulatory Submission Guidelines',
        'Guidelines for preparing regulatory submissions to FDA and EMA',
        'SOP',
        'Regulatory',
        'FDA, EMA, Submission, Guidelines',
        'Dr. Robert Chen',
        'Sarah Williams',
        'InReview',
        '1.2',
        'Regulatory_Submission_Guidelines_v1.2.pdf',
        3145728,
        'PDF',
        DATEADD(day, -10, GETUTCDATE()),
        NULL,
        'Regulatory Affairs',
        'Global Submissions',
        'Medium',
        DATEADD(day, 5, GETUTCDATE()),
        'Please review section 3.2 regarding EMA requirements',
        'Dr. Lisa Wong',
        DATEADD(day, -2, GETUTCDATE()),
        NULL,
        NULL,
        'System',
        1
    ),

    -- Document 3: Approved
    (
        'Quality Control Procedures for Manufacturing',
        'Standard operating procedures for quality control in the manufacturing process',
        'SOP',
        'Manufacturing',
        'QC, Manufacturing, SOP',
        'Michael Brown',
        'Jennifer Lee',
        'Approved',
        '2.0',
        'QC_Manufacturing_Procedures_v2.0.pdf',
        5242880,
        'PDF',
        DATEADD(day, -30, GETUTCDATE()),
        NULL,
        'Quality Assurance',
        'Manufacturing Excellence',
        'High',
        DATEADD(day, -5, GETUTCDATE()),
        'Approved with minor changes to section 4.2',
        'David Wilson',
        DATEADD(day, -7, GETUTCDATE()),
        'James Thompson',
        DATEADD(day, -3, GETUTCDATE()),
        'System',
        1
    ),

    -- Document 4: Published
    (
        'Annual Safety Report 2023',
        'Comprehensive safety report for all products for the year 2023',
        'Report',
        'Safety',
        'Safety, Annual Report, 2023',
        'Dr. Elizabeth Taylor',
        NULL,
        'Published',
        '1.0',
        'Annual_Safety_Report_2023.pdf',
        10485760,
        'PDF',
        DATEADD(day, -45, GETUTCDATE()),
        DATEADD(day, -40, GETUTCDATE()),
        'Pharmacovigilance',
        'Annual Reporting',
        'Medium',
        DATEADD(day, -42, GETUTCDATE()),
        'Final report approved without changes',
        'Dr. Richard Davis',
        DATEADD(day, -43, GETUTCDATE()),
        'Dr. Susan Miller',
        DATEADD(day, -41, GETUTCDATE()),
        'System',
        1
    ),

    -- Document 5: Archived
    (
        'Marketing Materials for Product Launch 2022',
        'Marketing materials and campaign strategy for the 2022 product launch',
        'Marketing',
        'Commercial',
        'Marketing, Product Launch, 2022',
        'Amanda Johnson',
        'Thomas White, Jessica Brown',
        'Archived',
        '3.0',
        'Marketing_Materials_2022_Final.pptx',
        15728640,
        'PPTX',
        DATEADD(day, -365, GETUTCDATE()),
        DATEADD(day, -360, GETUTCDATE()),
        'Marketing',
        'Product Launch 2022',
        'Low',
        DATEADD(day, -370, GETUTCDATE()),
        'Approved and implemented successfully',
        'Mark Robinson',
        DATEADD(day, -368, GETUTCDATE()),
        'Catherine Lewis',
        DATEADD(day, -365, GETUTCDATE()),
        'System',
        1
    );

    -- Continue with more sample authoring items
    INSERT INTO Authoring (
        Title, Description, AuthoringType, Category, Tags, Author, CoAuthors, Status, Version,
        FileName, FileSize, FileFormat, LastModified, PublishedDate, Department, Project,
        Priority, DueDate, ReviewComments, ReviewedBy, ReviewedDate, ApprovedBy, ApprovedDate,
        CreatedBy, IsActive
    )
    VALUES
    -- Document 6: Draft (Overdue)
    (
        'Patient Information Leaflet - Drug ABC',
        'Information leaflet for patients describing usage, benefits and side effects of Drug ABC',
        'Patient Material', 'Medical', 'PIL, Patient Information, Drug ABC', 'Dr. Sarah Johnson',
        NULL, 'Draft', '0.3', 'Patient_Leaflet_ABC_Draft.docx', 1048576, 'DOCX',
        DATEADD(day, -20, GETUTCDATE()), NULL, 'Medical Affairs', 'Patient Education', 'High',
        DATEADD(day, -1, GETUTCDATE()), NULL, NULL, NULL, NULL, NULL, 'System', 1
    ),

    -- Document 7: In Review
    (
        'Clinical Study Report - Study XYZ-123',
        'Final clinical study report for the XYZ-123 Phase III trial',
        'Report', 'Clinical', 'CSR, Study XYZ-123, Phase III', 'Dr. William Harris',
        'Dr. Emma Thompson, Dr. Robert Garcia', 'InReview', '0.9', 'CSR_XYZ-123_Draft.docx',
        8388608, 'DOCX', DATEADD(day, -8, GETUTCDATE()), NULL, 'Clinical Development',
        'XYZ-123 Study', 'High', DATEADD(day, 10, GETUTCDATE()),
        'Please review statistical analysis section', 'Dr. Patricia Moore',
        DATEADD(day, -3, GETUTCDATE()), NULL, NULL, 'System', 1
    ),

    -- Document 8: Approved
    (
        'Laboratory Standard Operating Procedures',
        'Comprehensive SOPs for all laboratory operations and testing procedures',
        'SOP', 'Laboratory', 'Lab, SOP, Testing', 'Dr. Kevin Wilson', 'Dr. Laura Martinez',
        'Approved', '2.1', 'Lab_SOP_v2.1.pdf', 4194304, 'PDF',
        DATEADD(day, -15, GETUTCDATE()), NULL, 'Laboratory Operations', 'Lab Excellence',
        'Medium', DATEADD(day, -5, GETUTCDATE()), 'Approved with updates to safety protocols',
        'Dr. Michelle Lee', DATEADD(day, -7, GETUTCDATE()), 'Dr. Jonathan Clark',
        DATEADD(day, -5, GETUTCDATE()), 'System', 1
    ),

    -- Document 9: Published
    (
        'Employee Training Manual - GMP Compliance',
        'Training manual for all employees on Good Manufacturing Practice compliance',
        'Training', 'Compliance', 'GMP, Training, Compliance', 'Rachel Adams', 'Daniel Wright',
        'Published', '1.5', 'GMP_Training_Manual_v1.5.pdf', 7340032, 'PDF',
        DATEADD(day, -60, GETUTCDATE()), DATEADD(day, -55, GETUTCDATE()), 'Human Resources',
        'Employee Training', 'Medium', DATEADD(day, -58, GETUTCDATE()),
        'Final version approved for company-wide distribution', 'Steven Jackson',
        DATEADD(day, -57, GETUTCDATE()), 'Victoria Nelson', DATEADD(day, -56, GETUTCDATE()),
        'System', 1
    ),

    -- Document 10: Draft
    (
        'Product Development Strategy 2024-2025',
        'Strategic plan for product development initiatives for the next two years',
        'Strategy', 'R&D', 'Strategy, Product Development, 2024, 2025', 'Dr. Christopher Evans',
        'Dr. Olivia Martin, James Wilson', 'Draft', '0.2', 'Product_Dev_Strategy_2024-2025_Draft.pptx',
        6291456, 'PPTX', DATEADD(day, -3, GETUTCDATE()), NULL, 'Research & Development',
        'Strategic Planning', 'High', DATEADD(day, 30, GETUTCDATE()), NULL, NULL, NULL,
        NULL, NULL, 'System', 1
    );

    PRINT 'Sample authoring data inserted successfully!';
END
ELSE
BEGIN
    PRINT 'Sample authoring data already exists.';
END
GO

-- =============================================
-- 5. TEST THE STORED PROCEDURE
-- =============================================

-- Test the stored procedure
PRINT 'Testing GetAuthoring_Sample stored procedure...';
EXEC GetAuthoring_Sample;
GO

PRINT '==============================================';
PRINT 'AuthoringTool Module Database Setup Complete!';
PRINT '==============================================';
PRINT 'Summary:';
PRINT '- Authoring table created with indexes';
PRINT '- GetAuthoring_Sample stored procedure created';
PRINT '- 10 sample authoring items inserted';
PRINT '- All components tested successfully';
PRINT '==============================================';
GO
