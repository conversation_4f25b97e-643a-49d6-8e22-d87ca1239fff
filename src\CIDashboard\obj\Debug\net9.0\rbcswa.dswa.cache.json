{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Xy8eF9vxxDSsFk79GrhQfvc5xAuGaBarb0o6ZbNIDr4=", "2xe6z867lHvgg/w7iIKjImZfxcFRk+7grujRLcgoZqg=", "cdOZw8xsHXMjFAoTXzXEvaFBt6xtSIfR2zQuT8WcYOE=", "sOX7/0Lf3ePAx6vYBDQrEwXMK7ELPmsi4Apd/FPWfb8=", "is9pFugQe8ambO6y3CYf1ucbiJGaKMjiLXOw70Kvjc0=", "CEv3uzeiZkjbSmCAlippCQbCjQI5cb0iLZKkZ4YdaxI=", "9UzKg9pjSG7CuwDjBNbCIsSSfqPtEsJBw6IcKGaMmzM=", "j8EE4c2HKrXOFC6vYgFvzbGQUj+Xv/qQteX0BAMRDnc=", "mXu7D2Lw2l12VCkZu2aQ3APb5yHluW6j8v3Z9xCdtP4=", "dJGHxXauASizA6LVWbIPzCp7Wn6pmPOkfGzhkQmjG7I=", "nfPsrjh52F4XWG+PBUuaIB9owYaVOUKz7szfSqEzAtg=", "8OhzXWZQkYRKTeky1mft5AqNyOIusIrJRuoF60Bcdag=", "ZkvF+xFl6vp1dxZcXjjLgTS9DoQ04a5/awhhMrgqCc0=", "tfq0BHlbaiY721y6TfamTim0bJbDPGXcvaDpLfv/OCw=", "EwNREEShdesjKXBRI7jij7OmoqlvhgGkLfsDFEgW5xQ=", "ddnhHK1nvHOYo+tvNJzZk4YVGoTzDn/t2wQ3u1a4+VI=", "xVoQ03Q2CeZLN0yyzO5IaUGnuBIW3j50wzlbzmV2vQo=", "LeUabqCYdOPpmlad6XuIp6zQkfSIYp7sQ8QKGNfeoDc=", "PpvGPCiBwf43SyN4fTfaAGXcVU8qvdT4XDn3xeeadXo=", "RhxBwEmTeakabCIlS2X9rxnvcRVurNXQYkBMhPfOv+c=", "LoQb5O7UbquNPyeawJGjOlvJ8oyodL4xBrB8EsmGP+U=", "W0B4XLkBGkMJMI+Tw4yWpCPymCrzaO1/G0Gm8Lk+ExI=", "AQrT5WQJx0KY4rsT8+MEI1CTBtcwY/Js2EkkKfjJMdo=", "GRfRYUf8CPac1xX7C6q2pGkeRLiUcPL+8WeKFzEPCwk=", "ExS8PD8g+FSJ5qgWc2M7ZP1MP+G4h8s5GyP7BZIvckk=", "JAHhbeXo5bbDeGpbhi/the6KPVJ4veXZin8pOKxG1YE=", "A8ro0TDBzppLS1xIxdbcdIU3XjT345itqXKw1A5RnWA=", "Uvu5hYs/g1tFxcTk+5kV2V0q4xO5cT9TEGLZyMy1qNI=", "xofcXXVr9Yd39eQDb+xLSAeD7hDIZ/GwXKfYmUHFkig=", "sbBQ9D1B8xFEEKM52V2s5CDoM/kZ1uBM3gJkC71a9Yw=", "s0n9f6iZgKb1IapLaurSFhfeojqPhGoNcEFvVz13LTs=", "o2hu3imEOVSb0a4EGwRV4kWWWWMaCdPwQDfZs/agW/s=", "vNHajnduPaxtr4V6sVZbPj4cbUFnPqe5xRM5I51MHMg=", "8e5zUxWyi5gZoOiCQ+GIdD6bGSLAxkmeu8q1mspWySk=", "Ft/Ni4f37MGrN8YItljPSxxscmwuH7aCc4zQmo+tnao=", "mOZcR6f+4no1y2BDv7ouuZXW6kjkxxVm/HfJVqvtGwU=", "FyZf2uthQv+xUVIDMjx85MKRaL5ut53fRJMrhlEYGVg=", "qUYSqj9DOd5tllYBQAK2ICBfjnTyGr7puk1+nU5Q/Bk=", "r919yki8mzS7EiaBk3l9cyNvV0hihYYPzcCeWHocoWU=", "xR+C8YpR5brN7PqeW4upo8AX+yqkupjcKloJNMsy15Q=", "s5Bh51iFmpCIQNTyEeF6NI2gJVDTcXQi4yt7XNppT2Y=", "+VkD4l4J/qFLkaTjUnVb4Jg8dcGyxMFtUBB4unZgx54=", "q+h1Bg1qarpcGswM35z3mLd7bE5VoLsEvOciNPRW2T0=", "wSXRVNPRm+2mSGrsfy0KU623RBKuVQWTWhRNOKRwoW8=", "GENZeXBLgnJRVku2UHtI4TtEKDFN0n19g1LY6jSfezs=", "vhf2fhmiNdb4AL8q1olEwVSTLB3Ba2aXhtVt2riVjD4=", "w5V+5NGR/terBAQGKMvXQDN26FyFRqJi+8omWZhi8tE=", "p4KjIyp21ySt3hvsCZcq9XQv3LsXWGtzptijV0Mu+nQ=", "ltGB2xuL4b7PEyp1QneVDj+taW+THfefZVt3d45CI+c=", "Zv6wdCWHGHtNwiAnwJLXE9XZPm+lwfuuJXCMrtTNk9o=", "rOXVdhQtKDt4arrQMXVrGrXaNgWeLIwMEy680hpdsuQ=", "tH39lvTajN+FTmBPHwZZva9y80/qFfjmaZ2FXEsxdZw=", "PALlyrf52r/Njrcg2j3S+9hJ7LEohd1zFwADWPOnFMY=", "sysN8vvhcDyOemHJeq6ijMLB0e1PZ+V6dPZ9fuY6htA=", "2ZZZLUNLkJAxzTvKHgWQtOgKM/8ThDxu8zfMwGI5K9c=", "De0ma9NXA8AREtEQJyE/YxTOYir/Idp3Lj/SZv+qgno=", "2D+NS5AlulGE49k8BUgpPsIUE83bzaOmGPGZtuIPLoU=", "HY/jXycQsdwTs513wSERyh8VoN1fevsls5cTE65Xy/0=", "Dxhct7YsF+spYX6ArdhLj2Wq2nLx4G+62gQMD3Aaq8U=", "2G2gmTJt4DjfTWsfW9NOS8GW0rcijdd9cChcZxb7b1Y=", "/GRpoY89RXsd2DqmFe84Wrkx4wEgvyNGi16MWDegiss=", "0IlZnb+EtguRi+4wkYr3zFdAO6YeWf5l9bM1odEnchs=", "T+ANX5UIERrA5e4RphwOdG4qLrymsDI/omKCFXiPCPg=", "9jRDosANMPIOspOfqjM9eyDhqmQ28ZaYjRHi7AecEN4="], "CachedAssets": {"9jRDosANMPIOspOfqjM9eyDhqmQ28ZaYjRHi7AecEN4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\xhzg3r7vio-3x7szj784z.gz", "SourceId": "CIDashboard", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "CIDashboard#[.{fingerprint=3x7szj784z}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\CIDashboard.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zhvi7np8lx", "Integrity": "Cd123ZkOHGT7gDw9SI/HXFy1jYNfjVXOPI/R91yB6LI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\CIDashboard.bundle.scp.css", "FileLength": 544, "LastWriteTime": "2025-07-16T06:24:59.7561892+00:00"}, "T+ANX5UIERrA5e4RphwOdG4qLrymsDI/omKCFXiPCPg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\gl1prii4h4-3x7szj784z.gz", "SourceId": "CIDashboard", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "CIDashboard#[.{fingerprint=3x7szj784z}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CIDashboard.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zhvi7np8lx", "Integrity": "Cd123ZkOHGT7gDw9SI/HXFy1jYNfjVXOPI/R91yB6LI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CIDashboard.styles.css", "FileLength": 544, "LastWriteTime": "2025-07-16T06:24:59.76086+00:00"}, "0IlZnb+EtguRi+4wkYr3zFdAO6YeWf5l9bM1odEnchs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\51ix0q7zli-mlv21k5csn.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdl0higpfn", "Integrity": "wRqs30CSrwWDvG1bDMUjomAlSqRRTMplLSuLcrgYtHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 669, "LastWriteTime": "2025-07-16T06:24:59.7516383+00:00"}, "/GRpoY89RXsd2DqmFe84Wrkx4wEgvyNGi16MWDegiss=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\dtgmrsdzi0-87fc7y1x7t.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uiwq7himce", "Integrity": "2u2XFgZyC96dP/elrO5ny/maD/llVl8JsX3pBm2SWT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 42735, "LastWriteTime": "2025-07-16T06:24:59.7305406+00:00"}, "2G2gmTJt4DjfTWsfW9NOS8GW0rcijdd9cChcZxb7b1Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\xx8g4wkf32-muycvpuwrr.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gm2ug2dj20", "Integrity": "OK9mKWiOoFmOZ+xYop9MRSKE6LHMu/9ZTCPvluUOpcA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24089, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "Dxhct7YsF+spYX6ArdhLj2Wq2nLx4G+62gQMD3Aaq8U=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\5k9xvu17fi-2z0ns9nrw6.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yow8h3bl0n", "Integrity": "fhspAbITSYOUuRd6s5oYdr7V1DC3ibPbW5Siv7MGps8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68315, "LastWriteTime": "2025-07-16T06:24:59.7757262+00:00"}, "HY/jXycQsdwTs513wSERyh8VoN1fevsls5cTE65Xy/0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\h7x6etmz2p-ttgo8qnofa.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sdwt97mj8r", "Integrity": "0HH7P7Mtrs+2sBMSFXD8DJw0oV4iDB5wkeRFpSi113A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 53929, "LastWriteTime": "2025-07-16T06:24:59.7525691+00:00"}, "2D+NS5AlulGE49k8BUgpPsIUE83bzaOmGPGZtuIPLoU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\y6w7kqun4t-o1o13a6vjx.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lbv9l0z68", "Integrity": "bSlV8nIWgnEsf0ljiCRK0xw364UXDcqZZilq1CQ+At0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30413, "LastWriteTime": "2025-07-16T06:24:59.711965+00:00"}, "De0ma9NXA8AREtEQJyE/YxTOYir/Idp3Lj/SZv+qgno=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\aehmftas48-0i3buxo5is.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v75tr20pas", "Integrity": "q0KM+9yOLRYeTH/iGBE/HUYO08aXn2DmZ+yyIU6/kho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84055, "LastWriteTime": "2025-07-16T06:24:59.7561892+00:00"}, "2ZZZLUNLkJAxzTvKHgWQtOgKM/8ThDxu8zfMwGI5K9c=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\x3omocdzu6-x0q3zqp4vz.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tbedkqp182", "Integrity": "yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 670, "LastWriteTime": "2025-07-16T06:24:59.711965+00:00"}, "sysN8vvhcDyOemHJeq6ijMLB0e1PZ+V6dPZ9fuY6htA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\buq5r2vok8-ag7o75518u.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m7rg4chp1z", "Integrity": "Po4sWHCLIbaqYqyEYnIYtBO+dRjwxtbreif7YdpGQxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8053, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "PALlyrf52r/Njrcg2j3S+9hJ7LEohd1zFwADWPOnFMY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\1k65xdj1gg-lzl9nlhx6b.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w44s95dr06", "Integrity": "8HCMA1neUf8Vrpg7qfQgzCVX6n/hFKgcFl5RKQuVC1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14023, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "tH39lvTajN+FTmBPHwZZva9y80/qFfjmaZ2FXEsxdZw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\ooa4qfu22n-mrlpezrjn3.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ka6j29rtmm", "Integrity": "nJwi+an4Wr4A7wVYax2DH+rKvwNbT+YQBBWTeP8GLr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6382, "LastWriteTime": "2025-07-16T06:24:59.7305406+00:00"}, "rOXVdhQtKDt4arrQMXVrGrXaNgWeLIwMEy680hpdsuQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\j9wutv39jy-83jwlth58m.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvtge0zj2y", "Integrity": "u0Z1XIrPIXG0QZK+EQOG85U9O7JhC+GVwFW+Rcyvbyw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 13955, "LastWriteTime": "2025-07-16T06:24:59.711965+00:00"}, "Zv6wdCWHGHtNwiAnwJLXE9XZPm+lwfuuJXCMrtTNk9o=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\c0j8mnjh19-356vix0kms.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0vtotz30el", "Integrity": "1HkjQSVuV0/ji/X/lGeDTHeagBS0BwopMrHDQuwqgtU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 680, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "ltGB2xuL4b7PEyp1QneVDj+taW+THfefZVt3d45CI+c=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\oxykxg5iox-4v8eqarkd7.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tabrq1ho0f", "Integrity": "9X+6mMpWl0sRImYav09zbU/QCCt+GeH7cDib6C/z2lI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2202, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "p4KjIyp21ySt3hvsCZcq9XQv3LsXWGtzptijV0Mu+nQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\0l8dhvi0q3-47otxtyo56.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hj59c5yssu", "Integrity": "TWRCXMwzQLwwVw7TcG+7g24PsqDnarIGBgITeMiCp10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4663, "LastWriteTime": "2025-07-16T06:24:59.7598118+00:00"}, "w5V+5NGR/terBAQGKMvXQDN26FyFRqJi+8omWZhi8tE=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\s1mjev23iw-0j3bgjxly4.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s9vzkbm4vd", "Integrity": "EImWkSMXEOh40HFlhmq90VTXlOCYqWVzWn4vPxUhiA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55524, "LastWriteTime": "2025-07-16T06:24:59.7561892+00:00"}, "vhf2fhmiNdb4AL8q1olEwVSTLB3Ba2aXhtVt2riVjD4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\ouuzt06ums-63fj8s7r0e.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnp63td8x7", "Integrity": "YQ6ZOC5+9dlRHEgMrLJKheWx2C2Xk7AL5Hy36EOfqq8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16571, "LastWriteTime": "2025-07-16T06:24:59.7368715+00:00"}, "GENZeXBLgnJRVku2UHtI4TtEKDFN0n19g1LY6jSfezs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\18w69hqu1c-h1s4sie4z3.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fizolni560", "Integrity": "OeWySIbjCJXM4ZgVwiGmsjpLeFsT+qOpONsDqh8xk5M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64490, "LastWriteTime": "2025-07-16T06:24:59.7305406+00:00"}, "wSXRVNPRm+2mSGrsfy0KU623RBKuVQWTWhRNOKRwoW8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\blpyhx95xy-notf2xhcfb.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "winy7sfnv8", "Integrity": "mLLuMjFYOIYX8xSc8UD/TLCKJOjlhcscgrxC5va1aAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29657, "LastWriteTime": "2025-07-16T06:24:59.7619466+00:00"}, "q+h1Bg1qarpcGswM35z3mLd7bE5VoLsEvOciNPRW2T0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\1ovezh4um6-y7v9cxd14o.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03mhy7u5rs", "Integrity": "9Hc7N6ug4kO+ZAzS1pFxw7Hbp+FZaCKMnDLRAv1J/zw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56385, "LastWriteTime": "2025-07-16T06:24:59.7625427+00:00"}, "+VkD4l4J/qFLkaTjUnVb4Jg8dcGyxMFtUBB4unZgx54=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\lthtry7yli-jj8uyg4cgr.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6t36crcw3z", "Integrity": "Ng/NTAPb2HvIa++U+TsQnZ6UYlXnzyFuh5Df7vHJZgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18542, "LastWriteTime": "2025-07-16T06:24:59.7525691+00:00"}, "s5Bh51iFmpCIQNTyEeF6NI2gJVDTcXQi4yt7XNppT2Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\rjus2qmv5t-kbrnm935zg.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6e2hoibiq5", "Integrity": "oglZ6aWY/UXq+QsVhwhOwfIZbgvz7Dvjg/GfXmQsBVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64234, "LastWriteTime": "2025-07-16T06:24:59.7305406+00:00"}, "xR+C8YpR5brN7PqeW4upo8AX+yqkupjcKloJNMsy15Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\qzc9qlex0k-vr1egmr9el.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9z27fl73l7", "Integrity": "K+JV1bWtCSH6pUbk4PUJUao1Gc0EagazN8O9VnhFaPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28886, "LastWriteTime": "2025-07-16T06:24:59.7757262+00:00"}, "r919yki8mzS7EiaBk3l9cyNvV0hihYYPzcCeWHocoWU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\7ycpv9pbnh-iovd86k7lj.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4jjusqppj5", "Integrity": "YNK7tH4KLEhmt17m2X41sl74ZCDUilGpyhRBJKwG0P4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86518, "LastWriteTime": "2025-07-16T06:24:59.7757262+00:00"}, "qUYSqj9DOd5tllYBQAK2ICBfjnTyGr7puk1+nU5Q/Bk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\2r4eg14kvj-493y06b0oq.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18qy0lzppu", "Integrity": "QZ0MSDfNrGl5pbF5ppDikD9YeYfOenEETYBSBemyReU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23799, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "FyZf2uthQv+xUVIDMjx85MKRaL5ut53fRJMrhlEYGVg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\wh07eefz0g-6pdc2jztkx.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iwjfk8z8sl", "Integrity": "fgl+pethjpTJQrRxrFinuKNP9hP56Cpi71nXoWFk1oE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92277, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "mOZcR6f+4no1y2BDv7ouuZXW6kjkxxVm/HfJVqvtGwU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\bu6g938xex-6cfz1n2cew.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqu1wssclk", "Integrity": "Fd462fjZQtrrvL/sHbIIZF7ktZgtHombUabahz+Nhoo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44461, "LastWriteTime": "2025-07-16T06:24:59.7918527+00:00"}, "Ft/Ni4f37MGrN8YItljPSxxscmwuH7aCc4zQmo+tnao=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\3oravy62ni-ft3s53vfgj.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-07-16T06:24:59.7757262+00:00"}, "8e5zUxWyi5gZoOiCQ+GIdD6bGSLAxkmeu8q1mspWySk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\z6azaibqjm-pk9g2wxc8p.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3m4n8jcl3h", "Integrity": "5VQx+pcREAWRC7fSQ1OVsJtpTGF7SJ83RS8p2rovHHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31137, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "vNHajnduPaxtr4V6sVZbPj4cbUFnPqe5xRM5I51MHMg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\dh5drsn3pn-hrwsygsryq.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "o2hu3imEOVSb0a4EGwRV4kWWWWMaCdPwQDfZs/agW/s=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\cm6wodrxjf-37tfw0ft22.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7kvveri0mt", "Integrity": "h+uVSG7EhchzF96LorZkFbGFBO8oQowufr5PhxsGq8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33366, "LastWriteTime": "2025-07-16T06:24:59.7757262+00:00"}, "s0n9f6iZgKb1IapLaurSFhfeojqPhGoNcEFvVz13LTs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\d4fjbe4t4j-v0zj4ognzu.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-07-16T06:24:59.7757262+00:00"}, "sbBQ9D1B8xFEEKM52V2s5CDoM/kZ1uBM3gJkC71a9Yw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\znk4xroyxj-46ein0sx1k.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uhnhmx07wd", "Integrity": "zrRIXAC8ugCIlsRMgBBjTa8xli0BiiAqT375rZab79Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31118, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "xofcXXVr9Yd39eQDb+xLSAeD7hDIZ/GwXKfYmUHFkig=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\h885gi9g2p-pj5nd1wqec.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-07-16T06:24:59.7619466+00:00"}, "Uvu5hYs/g1tFxcTk+5kV2V0q4xO5cT9TEGLZyMy1qNI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\30497ibvwl-s35ty4nyc5.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp6dicxiq4", "Integrity": "MfXRRxefGD0S0k5f3gXwZNQW7ELiStY01dZj+IbLlaw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33478, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "A8ro0TDBzppLS1xIxdbcdIU3XjT345itqXKw1A5RnWA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\dyr6j0amsz-nvvlpmu67g.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "JAHhbeXo5bbDeGpbhi/the6KPVJ4veXZin8pOKxG1YE=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\sdlrev0kau-06098lyss8.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ukjvmro89", "Integrity": "QDcr93RhSeRIcvSG2UO4XpXn7SmN9PibCcICeplm1tk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11127, "LastWriteTime": "2025-07-16T06:24:59.7305406+00:00"}, "ExS8PD8g+FSJ5qgWc2M7ZP1MP+G4h8s5GyP7BZIvckk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\l85zyo48sf-j5mq2jizvt.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-07-16T06:24:59.711965+00:00"}, "GRfRYUf8CPac1xX7C6q2pGkeRLiUcPL+8WeKFzEPCwk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\w1ocy1myk1-tdbxkamptv.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "letd63iq5t", "Integrity": "49exWGtFTeHrxPgcV2SALyeXqbJ0Gx2BAW7ghwTbDHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12069, "LastWriteTime": "2025-07-16T06:24:59.7368715+00:00"}, "AQrT5WQJx0KY4rsT8+MEI1CTBtcwY/Js2EkkKfjJMdo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\qk24pl6q7l-c2oey78nd0.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-07-16T06:24:59.7625427+00:00"}, "W0B4XLkBGkMJMI+Tw4yWpCPymCrzaO1/G0Gm8Lk+ExI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\pgwu3r16rl-lcd1t2u6c8.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cy4j794sg8", "Integrity": "LPVzscuhkQC3vZzaHKyyJLBxtZ0JDszgPmlT3zj1wGg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11149, "LastWriteTime": "2025-07-16T06:24:59.7305406+00:00"}, "LoQb5O7UbquNPyeawJGjOlvJ8oyodL4xBrB8EsmGP+U=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\5597hwml7b-r4e9w2rdcm.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-07-16T06:24:59.711965+00:00"}, "RhxBwEmTeakabCIlS2X9rxnvcRVurNXQYkBMhPfOv+c=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\yvcw836bs6-khv3u5hwcm.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3lpifp44y", "Integrity": "9EqjQR8ugEerwGk0t0elN3RQ+XHT25kPo1+/yYXwvNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12127, "LastWriteTime": "2025-07-16T06:24:59.7368715+00:00"}, "PpvGPCiBwf43SyN4fTfaAGXcVU8qvdT4XDn3xeeadXo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\7o14ah2mlu-jd9uben2k1.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-07-16T06:24:59.7525691+00:00"}, "LeUabqCYdOPpmlad6XuIp6zQkfSIYp7sQ8QKGNfeoDc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\i4z9p6ohzo-dxx9fxp4il.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b8ltiz8u9h", "Integrity": "u85nx859JKxhSe8TTSe97CuakJmuU36EC4JdxkCZL3g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3231, "LastWriteTime": "2025-07-16T06:24:59.7305406+00:00"}, "xVoQ03Q2CeZLN0yyzO5IaUGnuBIW3j50wzlbzmV2vQo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\nexigx570s-ee0r1s7dh0.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "ddnhHK1nvHOYo+tvNJzZk4YVGoTzDn/t2wQ3u1a4+VI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\pwoka83p6k-rzd6atqjts.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vkbr9nomgm", "Integrity": "oSfmOJQjIULAN8mPr84YlRct/JVoFbWAn9Y7IWzBuoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3364, "LastWriteTime": "2025-07-16T06:24:59.7305406+00:00"}, "EwNREEShdesjKXBRI7jij7OmoqlvhgGkLfsDFEgW5xQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\yh6mw5ugvx-fsbi9cje9m.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-07-16T06:24:59.7438794+00:00"}, "tfq0BHlbaiY721y6TfamTim0bJbDPGXcvaDpLfv/OCw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\f8ceejd8i1-b7pk76d08c.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fx04t62wt", "Integrity": "z14pI3bmpzGTIVaJu6aKOcry1zSAu/H44LHt9J3bXFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3199, "LastWriteTime": "2025-07-16T06:24:59.7278685+00:00"}, "ZkvF+xFl6vp1dxZcXjjLgTS9DoQ04a5/awhhMrgqCc0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\d514hj1h8z-fvhpjtyr6v.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-07-16T06:24:59.711965+00:00"}, "8OhzXWZQkYRKTeky1mft5AqNyOIusIrJRuoF60Bcdag=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\cxowrri6u5-ub07r2b239.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "syqesifl59", "Integrity": "/Cti7KtgK7n0FoWSWRMvmhTwGSxieEUp+Ao+6Xxnmug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3375, "LastWriteTime": "2025-07-16T06:24:59.7278685+00:00"}, "nfPsrjh52F4XWG+PBUuaIB9owYaVOUKz7szfSqEzAtg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\fitoob8fry-cosvhxvwiu.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-07-16T06:24:59.7525691+00:00"}, "dJGHxXauASizA6LVWbIPzCp7Wn6pmPOkfGzhkQmjG7I=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\ebd8y5ziyh-k8d9w2qqmf.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ykjynei6kc", "Integrity": "Xll87KRp2km7kw+kDSFiBiyUQnZ7QCVIb8yJtRDXckQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6104, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "mXu7D2Lw2l12VCkZu2aQ3APb5yHluW6j8v3Z9xCdtP4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\4p36kwiphe-ausgxo2sd3.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-07-16T06:24:59.711965+00:00"}, "j8EE4c2HKrXOFC6vYgFvzbGQUj+Xv/qQteX0BAMRDnc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\stc2dsmjue-d7shbmvgxk.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ql842l5", "Integrity": "+UkMsfq0zPH03cQtfw9papLT2sHleN3bw7S1s5iLonY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6918, "LastWriteTime": "2025-07-16T06:24:59.7368715+00:00"}, "9UzKg9pjSG7CuwDjBNbCIsSSfqPtEsJBw6IcKGaMmzM=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\597oatkthf-aexeepp0ev.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-07-16T06:24:59.711965+00:00"}, "CEv3uzeiZkjbSmCAlippCQbCjQI5cb0iLZKkZ4YdaxI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\7fc90sb103-erw9l3u2r3.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s32t06avo3", "Integrity": "6OfodiC592J173EssQMUTEfylPvm7++5l9PRY+7T5Uo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6102, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "is9pFugQe8ambO6y3CYf1ucbiJGaKMjiLXOw70Kvjc0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\btz76511y3-c2jlpeoesf.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "sOX7/0Lf3ePAx6vYBDQrEwXMK7ELPmsi4Apd/FPWfb8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\5exuwmxthc-bqjiyaj88i.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p1uwaguxw6", "Integrity": "HQp0c6N8q5wOxo27/7+5nSrp8SBfCepJdWIMB3pb5bE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6915, "LastWriteTime": "2025-07-16T06:24:59.7278685+00:00"}, "cdOZw8xsHXMjFAoTXzXEvaFBt6xtSIfR2zQuT8WcYOE=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\64mj3sf8ai-xtxxf3hu2r.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fpxp8wntf7", "Integrity": "465WqepNnI4ENLgQJFXrL2gtI20GCfeLK3JAtugOzpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\js\\site.js", "FileLength": 190, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "2xe6z867lHvgg/w7iIKjImZfxcFRk+7grujRLcgoZqg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\nwkmc5wpil-61n19gt1b8.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lir1p715ud", "Integrity": "jcY0RXFOlcp4VNTCcYQtWVZZBlr+ZY+AOv6/0S5dnF8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\favicon.ico", "FileLength": 2432, "LastWriteTime": "2025-07-16T06:24:59.6963408+00:00"}, "Xy8eF9vxxDSsFk79GrhQfvc5xAuGaBarb0o6ZbNIDr4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\gz88ehsadv-b9sayid5wm.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wf4wcyqcqt", "Integrity": "fNKkLSNFLZMVTc2zVrpMXQqOwPU3sU4GWcfEbCR2V1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\css\\site.css", "FileLength": 319, "LastWriteTime": "2025-07-16T06:24:59.6879546+00:00"}}, "CachedCopyCandidates": {}}