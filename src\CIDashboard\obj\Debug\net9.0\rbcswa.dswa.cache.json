{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["cuIlYQR5POGjZPasl48ECHiMBUp0S77eyP5jD0pQuP8=", "lbhsUs81Pm3Q2kAZCjISyIFngCzez3TiLFqXP/D9PeQ=", "0smvApzFO5J2WPPuSSLRAZshiDXcFyj0SPYa8qq7n+g=", "kxWaiB2HfBjyFKLYScfXYW6ETwgETO3eIYaFhbz5qR4=", "MCLMOpiNWHvZd/6AQvRaYBMNdLYfzF4z2Wz0IImIdQ4=", "YNbnH/KUXwCnj+9g7nzkCjEY4zT7LuYHeDr415P/9F4=", "92HM/D8g3nfYtpmsTTOXPjhlFKqhDhVx31PCs+YZq+M=", "EQ2K04BtBe36SA0gpAUSjVfebtngQT4slycT5hyGQME=", "/TTF1ti0J+zRf+sonNdv6J/ch3usiXPTPMj78aMRr8o=", "CjS8rprsjQbr3fZ1RhXOTGRmo0OgvrhiI/pinXP9+y0=", "5O0oBProkDpQDnsJvjz4V4en7Kgf4woQMHUO4KGVcX8=", "iVCekp8vjTvfuj66N//0ypAvcLtoaRqzq1MBJerEIpM=", "Ur8HbmQH5dQSpAaM1lDD9AMkETsQuRi1pUap+sINgIg=", "Q0SK4iwR4ol5TbLm1QE1Q2Df6sEWoJ3aKOAYtAhdf58=", "PTdl+16wHBl+NHhyD43XmKUz9MpRMXnKgCkOhvs0CIQ=", "+UbfgxH5VWxVrmc1VauvwN6KZ0//a6PiKfr5IhBx8n8=", "m7wSc3pAbrIIFIBTHrEHa9SC5oy9JOgpJaUWsLXpyL4=", "kNgRfIpJhXuVp5jftL+5Dhi4cAMwHja0SvEclk4JVmg=", "GBkYDSJ4tfcg2s8zXILYDjojxcgwyaeRmyO2B6cfj38=", "7WiS4px1Yzi9Q+QLAt8/DXD6GB4WyavYkM3OdyTrE5k=", "j/ChZ/EwIzxL1LApdD6wvw4+LY0BhxhOJTCbMM+QTqs=", "84beXtWIZN6hYZQXPOR/2h+hHp7CAbyVeqw9vkfDMHg=", "/mUcy4tnD7vvZin+T+cQqoSu8OETDsjsNVQr2UETN08=", "xKkmEYemomBoUuet2NdhVX+LJYhC5Ssk+k42aY4qG1E=", "Nhzf4s7/SSFRj6Wwmo7iudm3VTJWHX40zBWpXxvA5k8=", "7GimPZCksxTaexUFxn3iqDvTpR+i3fM99S0B29nAeS4=", "+an89Ai4/EHlUf+JaVeXHsb+cX0P9ca0lNcpsaoWc/E=", "yH6xT+bCOKr0JWW7Me4mv4WqJSofPcCW2DzxP1meAHw=", "ymBHnTVQKgZNAAtqCSC1cONEskolJ64lF3sDJG6toIQ=", "j4ytk1vCj/tkzrBvHZRKuOJYJXSGCu9rMJM1GWEKzvs=", "7b3zvhjxhCXicxVNIlPvfK6CVUbmKJ70iyoIfIS8jdo=", "iJtM7kQrarOnwMRysnX9E3L/cLNyKfgge9P63xv/lUU=", "E1bxZuazmm4QgFx4DEUvRc0oA6e2FDhhzoCaFsN5FJw=", "VUc1cfUnGQ8Z2RRG2DeRhIFxs70sOvMClZz94XlWEu0=", "1TORxVTQZVkKIP3n+JKbn4gkupCSyksI7PII4pL7+gs=", "YTvS39PrEKTGyqEtsO1xoyZKk0ikgjvyCWtkCwazP6w=", "krqXj8PQ93A4zTnwcOH6r+N2zRQfko62+Fr3HbO03Hw=", "Dp/mAqkAnu7VQloe3Iu35AyyEWQVkbEfqIVlEI9qnbM=", "52Tprj094JKttc3mbjkxTZkX8LZUs1wXVZT38FpnT2o=", "nU3jNTXcKZoXUhHbReeWF1Jnx2lmEtoddqW7FHthWuA=", "2E6Q8gqaodm4P8R5PnVGmzCAq3dk/1TG0bTuJmUjQxk=", "nG96olfR14LMF9KBqamtAYm9/74M2LRmfyHYgLOdofc=", "JXviTQIOBc6rcO6X/+gcJo9g2PMzzFfheGXwQKYkzXI=", "Qxccyo5jXMhEVo4J84x0g4KtbGOI+3QAgd2N22RraJY=", "FXY4QU+kvo5iVmfP+MpOq9uC8PKLqnIYFQPLu3sV6M8=", "cSQyMP9ot1Cde4REJ4wwgtPbdxg3GXSTAmvkH52Vy6Q=", "77bZ7PaI02DR2jTj4uKgiyHKyME01+2gKFUnEnTPIEo=", "4J3E48E0CYrbaFWE5lRkCFv/AUOy1495o6FdjHCIDKU=", "zKrpWZ1LxfrcMgsWvp+Y+Z0Gq72czE9JCY1UVU0CoeU=", "LYAEOnNy/vEHUK7Aeo434x1PoIEd4NkclwgTDSLgQEk=", "WU0LMnalAyznP/rNw+9mfuwSu8+NXNsyWLWfruQyCJE=", "SeKK+ufIYGeYYR3oYL3B91zFUd4LVIBvOUDWA5hufIc=", "ZUV/B8d2fOAGSTI4BVMC3CsOu7NvbLYO1VRSQqhsyPs=", "hMBy0WHtwrQT1Z8gD9WG1U/z7Ud1UNinx3X6oCWBCRk=", "yCNZ3kTrzXjv9DX3t6kQA3qNiV7qJLwx+Svgp63bevk=", "aKSxgA9+Byi5YlcfvtwNJZUPZlvZhXRIECPHDme8liI=", "gVfrP6bLH+qm6qMbFOvAxUj+4e9K8eEZ2r5qUAfLwRo=", "PskFTCuCHwxuosw7QXvlHP8yCIUK+M0mmwub6pvobzE=", "THE9Pj9Yud7O9F/kGiu5Wfp/8sLy+SDNQ4llXWTk3d4=", "kCd0W37Z6zdYvOAnss7MAQtzW/xTtiKiz2wCuj/fX+I=", "snKN9ukQKd5tErSBdY1Bqxdct/YrKxz2if925NJvb/A=", "oC2OBvz4XcUMR+0+TcKnEkbgwwpwY7B0OcZwelQdmxQ=", "ayp7IhtaU5jdpmIndXxfHMnG3dIDUarOW2Q6B2itWqQ=", "H6s+h+D1r4lDHTmazNdh8U30Owj2XzBe+S3cuboRqT4="], "CachedAssets": {"H6s+h+D1r4lDHTmazNdh8U30Owj2XzBe+S3cuboRqT4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\xhzg3r7vio-3x7szj784z.gz", "SourceId": "CIDashboard", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "CIDashboard#[.{fingerprint=3x7szj784z}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\CIDashboard.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zhvi7np8lx", "Integrity": "Cd123ZkOHGT7gDw9SI/HXFy1jYNfjVXOPI/R91yB6LI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\CIDashboard.bundle.scp.css", "FileLength": 544, "LastWriteTime": "2025-07-17T05:06:44.0681458+00:00"}, "ayp7IhtaU5jdpmIndXxfHMnG3dIDUarOW2Q6B2itWqQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\gl1prii4h4-3x7szj784z.gz", "SourceId": "CIDashboard", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "CIDashboard#[.{fingerprint=3x7szj784z}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CIDashboard.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zhvi7np8lx", "Integrity": "Cd123ZkOHGT7gDw9SI/HXFy1jYNfjVXOPI/R91yB6LI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CIDashboard.styles.css", "FileLength": 544, "LastWriteTime": "2025-07-17T05:06:44.0644489+00:00"}, "oC2OBvz4XcUMR+0+TcKnEkbgwwpwY7B0OcZwelQdmxQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\51ix0q7zli-mlv21k5csn.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rdl0higpfn", "Integrity": "wRqs30CSrwWDvG1bDMUjomAlSqRRTMplLSuLcrgYtHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 669, "LastWriteTime": "2025-07-17T05:06:44.0559276+00:00"}, "snKN9ukQKd5tErSBdY1Bqxdct/YrKxz2if925NJvb/A=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\dtgmrsdzi0-87fc7y1x7t.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uiwq7himce", "Integrity": "2u2XFgZyC96dP/elrO5ny/maD/llVl8JsX3pBm2SWT0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 42735, "LastWriteTime": "2025-07-17T05:06:44.0559276+00:00"}, "kCd0W37Z6zdYvOAnss7MAQtzW/xTtiKiz2wCuj/fX+I=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\xx8g4wkf32-muycvpuwrr.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gm2ug2dj20", "Integrity": "OK9mKWiOoFmOZ+xYop9MRSKE6LHMu/9ZTCPvluUOpcA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24089, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "THE9Pj9Yud7O9F/kGiu5Wfp/8sLy+SDNQ4llXWTk3d4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\5k9xvu17fi-2z0ns9nrw6.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yow8h3bl0n", "Integrity": "fhspAbITSYOUuRd6s5oYdr7V1DC3ibPbW5Siv7MGps8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68315, "LastWriteTime": "2025-07-17T05:06:44.0909127+00:00"}, "PskFTCuCHwxuosw7QXvlHP8yCIUK+M0mmwub6pvobzE=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\h7x6etmz2p-ttgo8qnofa.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sdwt97mj8r", "Integrity": "0HH7P7Mtrs+2sBMSFXD8DJw0oV4iDB5wkeRFpSi113A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 53929, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "gVfrP6bLH+qm6qMbFOvAxUj+4e9K8eEZ2r5qUAfLwRo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\y6w7kqun4t-o1o13a6vjx.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lbv9l0z68", "Integrity": "bSlV8nIWgnEsf0ljiCRK0xw364UXDcqZZilq1CQ+At0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30413, "LastWriteTime": "2025-07-17T05:06:44.0500911+00:00"}, "aKSxgA9+Byi5YlcfvtwNJZUPZlvZhXRIECPHDme8liI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\aehmftas48-0i3buxo5is.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v75tr20pas", "Integrity": "q0KM+9yOLRYeTH/iGBE/HUYO08aXn2DmZ+yyIU6/kho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84055, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "yCNZ3kTrzXjv9DX3t6kQA3qNiV7qJLwx+Svgp63bevk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\x3omocdzu6-x0q3zqp4vz.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tbedkqp182", "Integrity": "yZU1lf/p2dEnCHUXYp8G4roAyod23x5MoR4DehwHfrA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 670, "LastWriteTime": "2025-07-17T05:06:44.0575983+00:00"}, "hMBy0WHtwrQT1Z8gD9WG1U/z7Ud1UNinx3X6oCWBCRk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\buq5r2vok8-ag7o75518u.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m7rg4chp1z", "Integrity": "Po4sWHCLIbaqYqyEYnIYtBO+dRjwxtbreif7YdpGQxM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8053, "LastWriteTime": "2025-07-17T05:06:44.0575983+00:00"}, "ZUV/B8d2fOAGSTI4BVMC3CsOu7NvbLYO1VRSQqhsyPs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\1k65xdj1gg-lzl9nlhx6b.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w44s95dr06", "Integrity": "8HCMA1neUf8Vrpg7qfQgzCVX6n/hFKgcFl5RKQuVC1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14023, "LastWriteTime": "2025-07-17T05:06:44.0559276+00:00"}, "SeKK+ufIYGeYYR3oYL3B91zFUd4LVIBvOUDWA5hufIc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\ooa4qfu22n-mrlpezrjn3.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ka6j29rtmm", "Integrity": "nJwi+an4Wr4A7wVYax2DH+rKvwNbT+YQBBWTeP8GLr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6382, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "WU0LMnalAyznP/rNw+9mfuwSu8+NXNsyWLWfruQyCJE=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\j9wutv39jy-83jwlth58m.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pvtge0zj2y", "Integrity": "u0Z1XIrPIXG0QZK+EQOG85U9O7JhC+GVwFW+Rcyvbyw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 13955, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "LYAEOnNy/vEHUK7Aeo434x1PoIEd4NkclwgTDSLgQEk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\c0j8mnjh19-356vix0kms.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0vtotz30el", "Integrity": "1HkjQSVuV0/ji/X/lGeDTHeagBS0BwopMrHDQuwqgtU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 680, "LastWriteTime": "2025-07-17T05:06:44.062184+00:00"}, "zKrpWZ1LxfrcMgsWvp+Y+Z0Gq72czE9JCY1UVU0CoeU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\oxykxg5iox-4v8eqarkd7.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tabrq1ho0f", "Integrity": "9X+6mMpWl0sRImYav09zbU/QCCt+GeH7cDib6C/z2lI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2202, "LastWriteTime": "2025-07-17T05:06:44.0559276+00:00"}, "4J3E48E0CYrbaFWE5lRkCFv/AUOy1495o6FdjHCIDKU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\0l8dhvi0q3-47otxtyo56.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hj59c5yssu", "Integrity": "TWRCXMwzQLwwVw7TcG+7g24PsqDnarIGBgITeMiCp10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4663, "LastWriteTime": "2025-07-17T05:06:44.0909127+00:00"}, "77bZ7PaI02DR2jTj4uKgiyHKyME01+2gKFUnEnTPIEo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\s1mjev23iw-0j3bgjxly4.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s9vzkbm4vd", "Integrity": "EImWkSMXEOh40HFlhmq90VTXlOCYqWVzWn4vPxUhiA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55524, "LastWriteTime": "2025-07-17T05:06:44.0909127+00:00"}, "cSQyMP9ot1Cde4REJ4wwgtPbdxg3GXSTAmvkH52Vy6Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\ouuzt06ums-63fj8s7r0e.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnp63td8x7", "Integrity": "YQ6ZOC5+9dlRHEgMrLJKheWx2C2Xk7AL5Hy36EOfqq8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16571, "LastWriteTime": "2025-07-17T05:06:44.0712719+00:00"}, "FXY4QU+kvo5iVmfP+MpOq9uC8PKLqnIYFQPLu3sV6M8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\18w69hqu1c-h1s4sie4z3.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fizolni560", "Integrity": "OeWySIbjCJXM4ZgVwiGmsjpLeFsT+qOpONsDqh8xk5M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64490, "LastWriteTime": "2025-07-17T05:06:44.0712719+00:00"}, "Qxccyo5jXMhEVo4J84x0g4KtbGOI+3QAgd2N22RraJY=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\blpyhx95xy-notf2xhcfb.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "winy7sfnv8", "Integrity": "mLLuMjFYOIYX8xSc8UD/TLCKJOjlhcscgrxC5va1aAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29657, "LastWriteTime": "2025-07-17T05:06:44.0874002+00:00"}, "JXviTQIOBc6rcO6X/+gcJo9g2PMzzFfheGXwQKYkzXI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\1ovezh4um6-y7v9cxd14o.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03mhy7u5rs", "Integrity": "9Hc7N6ug4kO+ZAzS1pFxw7Hbp+FZaCKMnDLRAv1J/zw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56385, "LastWriteTime": "2025-07-17T05:06:44.0976269+00:00"}, "nG96olfR14LMF9KBqamtAYm9/74M2LRmfyHYgLOdofc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\lthtry7yli-jj8uyg4cgr.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6t36crcw3z", "Integrity": "Ng/NTAPb2HvIa++U+TsQnZ6UYlXnzyFuh5Df7vHJZgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18542, "LastWriteTime": "2025-07-17T05:06:44.0712719+00:00"}, "2E6Q8gqaodm4P8R5PnVGmzCAq3dk/1TG0bTuJmUjQxk=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\rjus2qmv5t-kbrnm935zg.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6e2hoibiq5", "Integrity": "oglZ6aWY/UXq+QsVhwhOwfIZbgvz7Dvjg/GfXmQsBVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64234, "LastWriteTime": "2025-07-17T05:06:44.0712719+00:00"}, "nU3jNTXcKZoXUhHbReeWF1Jnx2lmEtoddqW7FHthWuA=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\qzc9qlex0k-vr1egmr9el.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9z27fl73l7", "Integrity": "K+JV1bWtCSH6pUbk4PUJUao1Gc0EagazN8O9VnhFaPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28886, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "52Tprj094JKttc3mbjkxTZkX8LZUs1wXVZT38FpnT2o=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\7ycpv9pbnh-iovd86k7lj.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4jjusqppj5", "Integrity": "YNK7tH4KLEhmt17m2X41sl74ZCDUilGpyhRBJKwG0P4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86518, "LastWriteTime": "2025-07-17T05:06:44.0976269+00:00"}, "Dp/mAqkAnu7VQloe3Iu35AyyEWQVkbEfqIVlEI9qnbM=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\2r4eg14kvj-493y06b0oq.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18qy0lzppu", "Integrity": "QZ0MSDfNrGl5pbF5ppDikD9YeYfOenEETYBSBemyReU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23799, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "krqXj8PQ93A4zTnwcOH6r+N2zRQfko62+Fr3HbO03Hw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\wh07eefz0g-6pdc2jztkx.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iwjfk8z8sl", "Integrity": "fgl+pethjpTJQrRxrFinuKNP9hP56Cpi71nXoWFk1oE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92277, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "YTvS39PrEKTGyqEtsO1xoyZKk0ikgjvyCWtkCwazP6w=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\bu6g938xex-6cfz1n2cew.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqu1wssclk", "Integrity": "Fd462fjZQtrrvL/sHbIIZF7ktZgtHombUabahz+Nhoo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44461, "LastWriteTime": "2025-07-17T05:06:44.1065558+00:00"}, "1TORxVTQZVkKIP3n+JKbn4gkupCSyksI7PII4pL7+gs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\3oravy62ni-ft3s53vfgj.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uom9ela1zq", "Integrity": "4gJzy0olXjj0z70VKojvdJzPHOLLNp9HrIQJNzkwxWE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91866, "LastWriteTime": "2025-07-17T05:06:44.0976269+00:00"}, "VUc1cfUnGQ8Z2RRG2DeRhIFxs70sOvMClZz94XlWEu0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\z6azaibqjm-pk9g2wxc8p.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3m4n8jcl3h", "Integrity": "5VQx+pcREAWRC7fSQ1OVsJtpTGF7SJ83RS8p2rovHHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 31137, "LastWriteTime": "2025-07-17T05:06:44.0712719+00:00"}, "E1bxZuazmm4QgFx4DEUvRc0oA6e2FDhhzoCaFsN5FJw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\dh5drsn3pn-hrwsygsryq.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2knqut3gi", "Integrity": "YedRlLwXS/p1OuS/d0RjDFf5lQsoglvovzJIcXC8mWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114877, "LastWriteTime": "2025-07-17T05:06:44.0874002+00:00"}, "iJtM7kQrarOnwMRysnX9E3L/cLNyKfgge9P63xv/lUU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\cm6wodrxjf-37tfw0ft22.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7kvveri0mt", "Integrity": "h+uVSG7EhchzF96LorZkFbGFBO8oQowufr5PhxsGq8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33366, "LastWriteTime": "2025-07-17T05:06:44.1065558+00:00"}, "7b3zvhjxhCXicxVNIlPvfK6CVUbmKJ70iyoIfIS8jdo=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\d4fjbe4t4j-v0zj4ognzu.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "77t5enldb7", "Integrity": "Ez3ceWLr2D4WzWEtd0AgNaoRd/oY3XS1P5pFtgjHfAs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91960, "LastWriteTime": "2025-07-17T05:06:44.1065558+00:00"}, "j4ytk1vCj/tkzrBvHZRKuOJYJXSGCu9rMJM1GWEKzvs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\znk4xroyxj-46ein0sx1k.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uhnhmx07wd", "Integrity": "zrRIXAC8ugCIlsRMgBBjTa8xli0BiiAqT375rZab79Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 31118, "LastWriteTime": "2025-07-17T05:06:44.0874002+00:00"}, "ymBHnTVQKgZNAAtqCSC1cONEskolJ64lF3sDJG6toIQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\h885gi9g2p-pj5nd1wqec.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glev5uv9kg", "Integrity": "pOh8WLH4j3VtIjwkYu8FxMwfZXN1u9bs0TmE+R/LeKU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 114902, "LastWriteTime": "2025-07-17T05:06:44.0681458+00:00"}, "yH6xT+bCOKr0JWW7Me4mv4WqJSofPcCW2DzxP1meAHw=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\30497ibvwl-s35ty4nyc5.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp6dicxiq4", "Integrity": "MfXRRxefGD0S0k5f3gXwZNQW7ELiStY01dZj+IbLlaw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33478, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "+an89Ai4/EHlUf+JaVeXHsb+cX0P9ca0lNcpsaoWc/E=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\dyr6j0amsz-nvvlpmu67g.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gru265j2n", "Integrity": "cBDhSnwBZjhD+2ai7wNkh+CWcMrzxwfOe6IVNDuakD4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24456, "LastWriteTime": "2025-07-17T05:06:44.0909127+00:00"}, "7GimPZCksxTaexUFxn3iqDvTpR+i3fM99S0B29nAeS4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\sdlrev0kau-06098lyss8.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ukjvmro89", "Integrity": "QDcr93RhSeRIcvSG2UO4XpXn7SmN9PibCcICeplm1tk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11127, "LastWriteTime": "2025-07-17T05:06:44.0681458+00:00"}, "Nhzf4s7/SSFRj6Wwmo7iudm3VTJWHX40zBWpXxvA5k8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\l85zyo48sf-j5mq2jizvt.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pnmw3mh9ht", "Integrity": "tYXT5KEqImURIzC3Qanl3vg3XPSckCiDY2iFihvBz4w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44237, "LastWriteTime": "2025-07-17T05:06:44.062184+00:00"}, "xKkmEYemomBoUuet2NdhVX+LJYhC5Ssk+k42aY4qG1E=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\w1ocy1myk1-tdbxkamptv.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "letd63iq5t", "Integrity": "49exWGtFTeHrxPgcV2SALyeXqbJ0Gx2BAW7ghwTbDHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12069, "LastWriteTime": "2025-07-17T05:06:44.0909127+00:00"}, "/mUcy4tnD7vvZin+T+cQqoSu8OETDsjsNVQr2UETN08=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\qk24pl6q7l-c2oey78nd0.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oiolxl3gck", "Integrity": "oCsmmiBhwTjmHD8BM0Q5rOz0kfUetk7G5Eydczr8npA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24487, "LastWriteTime": "2025-07-17T05:06:44.0874002+00:00"}, "84beXtWIZN6hYZQXPOR/2h+hHp7CAbyVeqw9vkfDMHg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\pgwu3r16rl-lcd1t2u6c8.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cy4j794sg8", "Integrity": "LPVzscuhkQC3vZzaHKyyJLBxtZ0JDszgPmlT3zj1wGg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11149, "LastWriteTime": "2025-07-17T05:06:44.0760725+00:00"}, "j/ChZ/EwIzxL1LApdD6wvw4+LY0BhxhOJTCbMM+QTqs=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\5597hwml7b-r4e9w2rdcm.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21g01g2f66", "Integrity": "JhJG+P11KpccX3eLLCfr9IvrrIGljBXVVu8i06COWfo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44266, "LastWriteTime": "2025-07-17T05:06:44.062184+00:00"}, "7WiS4px1Yzi9Q+QLAt8/DXD6GB4WyavYkM3OdyTrE5k=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\yvcw836bs6-khv3u5hwcm.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3lpifp44y", "Integrity": "9EqjQR8ugEerwGk0t0elN3RQ+XHT25kPo1+/yYXwvNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12127, "LastWriteTime": "2025-07-17T05:06:44.0650678+00:00"}, "GBkYDSJ4tfcg2s8zXILYDjojxcgwyaeRmyO2B6cfj38=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\7o14ah2mlu-jd9uben2k1.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ldvkm706vq", "Integrity": "J1KmNvA0YsTrxABdpOt20g2Pc9ye2G9nKQ+doaPxfWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15028, "LastWriteTime": "2025-07-17T05:06:44.0712719+00:00"}, "kNgRfIpJhXuVp5jftL+5Dhi4cAMwHja0SvEclk4JVmg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\i4z9p6ohzo-dxx9fxp4il.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b8ltiz8u9h", "Integrity": "u85nx859JKxhSe8TTSe97CuakJmuU36EC4JdxkCZL3g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3231, "LastWriteTime": "2025-07-17T05:06:44.0644489+00:00"}, "m7wSc3pAbrIIFIBTHrEHa9SC5oy9JOgpJaUWsLXpyL4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\nexigx570s-ee0r1s7dh0.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kqk14ew2nl", "Integrity": "PBumXrpJefIUX366kZ07GYowOsT9YFX4xEFYFHevQag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25726, "LastWriteTime": "2025-07-17T05:06:44.0500911+00:00"}, "+UbfgxH5VWxVrmc1VauvwN6KZ0//a6PiKfr5IhBx8n8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\pwoka83p6k-rzd6atqjts.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vkbr9nomgm", "Integrity": "oSfmOJQjIULAN8mPr84YlRct/JVoFbWAn9Y7IWzBuoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3364, "LastWriteTime": "2025-07-17T05:06:44.0681458+00:00"}, "PTdl+16wHBl+NHhyD43XmKUz9MpRMXnKgCkOhvs0CIQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\yh6mw5ugvx-fsbi9cje9m.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ggfb0v5ylw", "Integrity": "ADH59/NtYFvapbe8WFHRcZlFJ2Si4p3FnbxP5fFT5lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12568, "LastWriteTime": "2025-07-17T05:06:44.062184+00:00"}, "Q0SK4iwR4ol5TbLm1QE1Q2Df6sEWoJ3aKOAYtAhdf58=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\f8ceejd8i1-b7pk76d08c.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fx04t62wt", "Integrity": "z14pI3bmpzGTIVaJu6aKOcry1zSAu/H44LHt9J3bXFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3199, "LastWriteTime": "2025-07-17T05:06:44.0591068+00:00"}, "Ur8HbmQH5dQSpAaM1lDD9AMkETsQuRi1pUap+sINgIg=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\d514hj1h8z-fvhpjtyr6v.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5b7ig2cj79", "Integrity": "fzLlXEmw9871JGb59kzfItifyrM9MgjHNQirf4lIS64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25714, "LastWriteTime": "2025-07-17T05:06:44.0591068+00:00"}, "iVCekp8vjTvfuj66N//0ypAvcLtoaRqzq1MBJerEIpM=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\cxowrri6u5-ub07r2b239.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "syqesifl59", "Integrity": "/Cti7KtgK7n0FoWSWRMvmhTwGSxieEUp+Ao+6Xxnmug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3375, "LastWriteTime": "2025-07-17T05:06:44.0644489+00:00"}, "5O0oBProkDpQDnsJvjz4V4en7Kgf4woQMHUO4KGVcX8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\fitoob8fry-cosvhxvwiu.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f178oapzb7", "Integrity": "XVqmPhc00T/7zYDUfSy4bYaZ4YjTbapktP6tFnzH6xM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 14093, "LastWriteTime": "2025-07-17T05:06:44.0650678+00:00"}, "CjS8rprsjQbr3fZ1RhXOTGRmo0OgvrhiI/pinXP9+y0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\ebd8y5ziyh-k8d9w2qqmf.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ykjynei6kc", "Integrity": "Xll87KRp2km7kw+kDSFiBiyUQnZ7QCVIb8yJtRDXckQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 6104, "LastWriteTime": "2025-07-17T05:06:44.0591068+00:00"}, "/TTF1ti0J+zRf+sonNdv6J/ch3usiXPTPMj78aMRr8o=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\4p36kwiphe-ausgxo2sd3.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lnst782kog", "Integrity": "ryK/tFqrYO6GpAIb5XFeLy8cUXDiklFAEkL+JelQZRo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 33061, "LastWriteTime": "2025-07-17T05:06:44.062184+00:00"}, "EQ2K04BtBe36SA0gpAUSjVfebtngQT4slycT5hyGQME=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\stc2dsmjue-d7shbmvgxk.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "316ql842l5", "Integrity": "+UkMsfq0zPH03cQtfw9papLT2sHleN3bw7S1s5iLonY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6918, "LastWriteTime": "2025-07-17T05:06:44.0644489+00:00"}, "92HM/D8g3nfYtpmsTTOXPjhlFKqhDhVx31PCs+YZq+M=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\597oatkthf-aexeepp0ev.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3latwtrz94", "Integrity": "1/7f+GraCXppQxdIoS5cuJ0z+sz2LI680XxYJg3fcdM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 14072, "LastWriteTime": "2025-07-17T05:06:44.0591068+00:00"}, "YNbnH/KUXwCnj+9g7nzkCjEY4zT7LuYHeDr415P/9F4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\7fc90sb103-erw9l3u2r3.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s32t06avo3", "Integrity": "6OfodiC592J173EssQMUTEfylPvm7++5l9PRY+7T5Uo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 6102, "LastWriteTime": "2025-07-17T05:06:44.054783+00:00"}, "MCLMOpiNWHvZd/6AQvRaYBMNdLYfzF4z2Wz0IImIdQ4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\btz76511y3-c2jlpeoesf.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bpa5g0wlhg", "Integrity": "keojCSbIk+KcHuoejTbPFk8zgraA9s/7WmedJipnoMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 33080, "LastWriteTime": "2025-07-17T05:06:44.0591068+00:00"}, "kxWaiB2HfBjyFKLYScfXYW6ETwgETO3eIYaFhbz5qR4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\5exuwmxthc-bqjiyaj88i.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p1uwaguxw6", "Integrity": "HQp0c6N8q5wOxo27/7+5nSrp8SBfCepJdWIMB3pb5bE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6915, "LastWriteTime": "2025-07-17T05:06:44.062184+00:00"}, "0smvApzFO5J2WPPuSSLRAZshiDXcFyj0SPYa8qq7n+g=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\64mj3sf8ai-xtxxf3hu2r.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fpxp8wntf7", "Integrity": "465WqepNnI4ENLgQJFXrL2gtI20GCfeLK3JAtugOzpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\js\\site.js", "FileLength": 190, "LastWriteTime": "2025-07-17T05:06:44.0575983+00:00"}, "lbhsUs81Pm3Q2kAZCjISyIFngCzez3TiLFqXP/D9PeQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\nwkmc5wpil-61n19gt1b8.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lir1p715ud", "Integrity": "jcY0RXFOlcp4VNTCcYQtWVZZBlr+ZY+AOv6/0S5dnF8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\favicon.ico", "FileLength": 2432, "LastWriteTime": "2025-07-17T05:06:44.0553451+00:00"}, "cuIlYQR5POGjZPasl48ECHiMBUp0S77eyP5jD0pQuP8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\gz88ehsadv-b9sayid5wm.gz", "SourceId": "CIDashboard", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/CIDashboard", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wf4wcyqcqt", "Integrity": "fNKkLSNFLZMVTc2zVrpMXQqOwPU3sU4GWcfEbCR2V1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\CISolution\\src\\CIDashboard\\wwwroot\\css\\site.css", "FileLength": 319, "LastWriteTime": "2025-07-17T05:06:44.047157+00:00"}}, "CachedCopyCandidates": {}}