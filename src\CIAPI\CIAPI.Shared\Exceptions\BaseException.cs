using System.Net;

namespace CIAPI.Shared.Exceptions;

/// <summary>
/// Base exception class for all custom exceptions in the CI Solution
/// </summary>
public abstract class BaseException : Exception
{
    /// <summary>
    /// HTTP status code to return for this exception
    /// </summary>
    public abstract HttpStatusCode StatusCode { get; }
    
    /// <summary>
    /// Error code for categorizing the exception
    /// </summary>
    public abstract string ErrorCode { get; }
    
    /// <summary>
    /// Additional details about the exception
    /// </summary>
    public virtual object? Details { get; set; }
    
    /// <summary>
    /// Correlation ID for tracking the request
    /// </summary>
    public string? CorrelationId { get; set; }
    
    /// <summary>
    /// Module where the exception occurred
    /// </summary>
    public string? Module { get; set; }
    
    /// <summary>
    /// User ID if available
    /// </summary>
    public string? UserId { get; set; }
    
    /// <summary>
    /// Additional context information
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();

    protected BaseException(string message) : base(message)
    {
    }

    protected BaseException(string message, Exception innerException) : base(message, innerException)
    {
    }

    protected BaseException(string message, string correlationId) : base(message)
    {
        CorrelationId = correlationId;
    }

    protected BaseException(string message, Exception innerException, string correlationId) 
        : base(message, innerException)
    {
        CorrelationId = correlationId;
    }
    
    /// <summary>
    /// Add context information to the exception
    /// </summary>
    public BaseException AddContext(string key, object value)
    {
        Context[key] = value;
        return this;
    }
    
    /// <summary>
    /// Set the module where the exception occurred
    /// </summary>
    public BaseException SetModule(string module)
    {
        Module = module;
        return this;
    }
    
    /// <summary>
    /// Set the user ID
    /// </summary>
    public BaseException SetUserId(string userId)
    {
        UserId = userId;
        return this;
    }
}
