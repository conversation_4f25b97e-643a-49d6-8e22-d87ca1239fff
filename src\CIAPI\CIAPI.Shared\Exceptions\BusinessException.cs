using System.Net;

namespace CIAPI.Shared.Exceptions;

/// <summary>
/// Exception for business logic violations
/// </summary>
public class BusinessException : BaseException
{
    public override HttpStatusCode StatusCode => HttpStatusCode.BadRequest;
    public override string ErrorCode => "BUSINESS_RULE_VIOLATION";

    public BusinessException(string message) : base(message)
    {
    }

    public BusinessException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public BusinessException(string message, string correlationId) : base(message, correlationId)
    {
    }

    public BusinessException(string message, Exception innerException, string correlationId) 
        : base(message, innerException, correlationId)
    {
    }
}
